{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue?vue&type=style&index=0&id=26084dc2&scoped=true&lang=css", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue", "mtime": 1749196211334}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749193686285}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749193687363}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749193686702}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue"], "names": [], "mappings": ";AAkJA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACX,EAAE;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAChD;;AAEA,CAAC,CAAC,CAAC,EAAE;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAClC;;AAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,EAAE;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACd;IACA,CAAC,CAAC,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACd;AACJ;;AAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;;AAEA,EAAE;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,EAAE;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACzB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AAClB", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/Login.vue", "sourceRoot": "", "sourcesContent": ["<template>  \r\n  <div class=\"body\">  \r\n    <div class=\"login-container\">  \r\n           <h2 style=\"text-align: center; font-size: 24px; margin-bottom: 24px;\">医院挂号预约系统</h2>\r\n            <form>  \r\n               <div>  \r\n                   <label for=\"uaccount\">账号</label>  \r\n                   <input type=\"text\" id=\"uaccount\" placeholder=\"请输入账号\" required v-model=\"loginModel.username\">  \r\n               </div>  \r\n               <div>  \r\n                   <label for=\"password\">密码</label>  \r\n                   <input type=\"password\" id=\"password\" placeholder=\"请输入密码\" required v-model=\"loginModel.password\">  \r\n               </div>  \r\n                   <div>  \r\n                   <label>身份</label>  \r\n                   <div class=\"role-selection\">  \r\n                              <el-radio label=\"管理员\" v-model=\"loginModel.radio\">管理员</el-radio>\r\n      <el-radio label=\"医生\" v-model=\"loginModel.radio\">医生</el-radio>\r\n \r\n                   </div>  \r\n               </div> \r\n    \r\n               <button type=\"button\" @click=\"login\">登录</button>  \r\n           </form>  \r\n            \r\n \r\n<!--           <p>还没有账户？ <a href=\"#\">注册</a></p> --> \r\n       </div>  \r\n       <div class=\"bubble\" style=\"width: 60px; height: 60px; left: 20%; animation-delay: 0s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 40px; height: 40px; left: 50%; animation-delay: 2s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 80px; height: 80px; left: 80%; animation-delay: 4s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 30px; height: 30px; left: 30%; animation-delay: 1s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 50px; height: 50px; left: 70%; animation-delay: 3s;\"></div>   \r\n   </div>  \r\n</template>\r\n<script>\r\nimport request, { base } from \"../../utils/http\";\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      year: new Date().getFullYear(),\r\n      loginModel: {\r\n        username: \"\",\r\n        password: \"\",\r\n        radio: \"管理员\",\r\n      },\r\n      loginModel2: {},\r\n     \r\n    };\r\n  },\r\n  mounted() {},\r\n  created() {\r\n    \r\n  },\r\n  methods: {\r\n    login() {\r\n      let that = this;  \r\n\r\n      if (that.loginModel.username == \"\") {\r\n        that.$message({\r\n          message: \"请输入账号\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      if (that.loginModel.password == \"\") {\r\n        that.$message({\r\n          message: \"请输入密码\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }   \r\n      \r\n      this.loading = true;\r\n     var role = that.loginModel.radio; //获取身份\r\nif (role == '管理员') {\r\n      let url = base + \"/admin/login\";\r\n      this.loginModel2.aname = this.loginModel.username;\r\n      this.loginModel2.loginpassword = this.loginModel.password;\r\n      request.post(url, this.loginModel2).then((res) => {\r\n        this.loading = false;\r\n        if (res.code == 200) {\r\n          console.log(JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"userLname\", res.resdata.aname);\r\n          sessionStorage.setItem(\"role\", \"管理员\");\r\n          this.$router.push(\"/main\");\r\n        } else {\r\n          this.$message({\r\n            message: res.msg,\r\n            type: \"error\",\r\n          });\r\n        }\r\n      }).catch((error) => {\r\n        this.loading = false;\r\n        // 模拟登录成功用于演示\r\n        console.log('后端服务器未连接，模拟管理员登录');\r\n        const mockUser = { aid: 1, aname: this.loginModel.username };\r\n        sessionStorage.setItem(\"user\", JSON.stringify(mockUser));\r\n        sessionStorage.setItem(\"userLname\", this.loginModel.username);\r\n        sessionStorage.setItem(\"role\", \"管理员\");\r\n        this.$message.success('模拟登录成功（后端服务器未连接）');\r\n        this.$router.push(\"/main\");\r\n      });\r\n          }\r\nelse if (role == '医生') {\r\n      let url = base + \"/doctor/login\";\r\n      this.loginModel2.daccount = this.loginModel.username;\r\n      this.loginModel2.password = this.loginModel.password;\r\n      request.post(url, this.loginModel2).then((res) => {\r\n        this.loading = false;\r\n        if (res.code == 200) {\r\n          console.log(JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"userLname\", res.resdata.daccount);\r\n          sessionStorage.setItem(\"role\", \"医生\");\r\n          this.$router.push(\"/main\");\r\n        } else {\r\n          this.$message({\r\n            message: res.msg,\r\n            type: \"error\",\r\n          });\r\n        }\r\n      }).catch((error) => {\r\n        this.loading = false;\r\n        // 模拟登录成功用于演示\r\n        console.log('后端服务器未连接，模拟医生登录');\r\n        const mockUser = { did: 1, daccount: this.loginModel.username };\r\n        sessionStorage.setItem(\"user\", JSON.stringify(mockUser));\r\n        sessionStorage.setItem(\"userLname\", this.loginModel.username);\r\n        sessionStorage.setItem(\"role\", \"医生\");\r\n        this.$message.success('模拟登录成功（后端服务器未连接）');\r\n        this.$router.push(\"/main\");\r\n      });\r\n          }\r\n    \r\n     \r\n    },\r\n    \r\n    \r\n  },\r\n};\r\n</script>\r\n   \r\n<style scoped>  \r\n@import \"../assets/css/body.css\";\r\n/* 全局样式重置 */  \r\n* {  \r\n    margin: 0;  \r\n    padding: 0;  \r\n    box-sizing: border-box; /* 确保元素的宽高包括内边距和边框 */  \r\n}  \r\n\r\nbody {  \r\n    width: 100%;  \r\n    height: 100%;  \r\n    overflow: hidden; /* 确保没有滚动条 */  \r\n}  \r\n\r\n.body {  \r\n    background: linear-gradient(to bottom right, #009688, #8BC34A);  \r\n    display: flex;  \r\n    align-items: center;  \r\n    justify-content: center;  \r\n    height: 100vh;  \r\n}  \r\n\r\n.login-container {  \r\n    background: white;  \r\n    border-radius: 8px;  \r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);  \r\n    padding: 32px;  \r\n    position: relative;  \r\n    z-index: 10;  \r\n    transition: transform 0.3s ease;  \r\n    width: 400px;  \r\n}  \r\n\r\n.login-container:hover {  \r\n    transform: scale(1.05);  \r\n}  \r\n\r\n.bubble {  \r\n    position: absolute;  \r\n    bottom: -100px;  \r\n    border-radius: 50%;  \r\n    background: rgba(255, 255, 255, 0.6);  \r\n    animation: rise 10s infinite;  \r\n}  \r\n\r\n@keyframes rise {  \r\n    0% {  \r\n        transform: translateY(0);  \r\n        opacity: 1;  \r\n    }  \r\n    100% {  \r\n        transform: translateY(-600px);  \r\n        opacity: 0;  \r\n    }  \r\n}  \r\n\r\ninput {  \r\n    width: 100%;  \r\n    padding: 10px;  \r\n    margin: 10px 0;  \r\n    border: 1px solid #ccc;  \r\n    border-radius: 4px;  \r\n}  \r\n\r\nbutton {  \r\n    width: 100%;  \r\n    padding: 10px;  \r\n    background-color: #8BC34A;  \r\n    color: white;  \r\n    border: none;  \r\n    border-radius: 4px;  \r\n    cursor: pointer;  \r\n    transition: background-color 0.2s;  \r\n}  \r\n\r\nbutton:hover {  \r\n    background-color: #81d522;  \r\n}  \r\n\r\np {  \r\n    text-align: center;  \r\n    color: #666;  \r\n}  \r\n\r\na {  \r\n    color: #8BC34A;  \r\n    text-decoration: none;  \r\n}  \r\n\r\na:hover {  \r\n    text-decoration: underline;  \r\n}  \r\n\r\n.role-selection {  \r\n    display: flex;  \r\n    align-items: center;  \r\n    margin: 10px 0;  \r\n}  \r\n\r\n.role-selection input {  \r\n    margin-right: 5px;  \r\n}  \r\n\r\ninput[type=\"radio\"] {  \r\n    display: flex;  \r\n    width: 50px;  \r\n    align-items: center;  \r\n    margin: 10px 0;  \r\n}  \r\n</style>\r\n\r\n"]}]}