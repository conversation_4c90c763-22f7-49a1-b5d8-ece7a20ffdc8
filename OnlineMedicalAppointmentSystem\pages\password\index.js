const App = getApp();
const navigationBarHeight = getApp().diygwstatusBarHeight + 44 + "px";
Page({
  data: {},

  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
  },

  //页面显示
  onShow() {
    this.init();
  },

  async init() {
    this.getForm1();
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },

  //设置输入验证
  getForm1() {
    this.WxValidate = App.WxValidate({
      pass1: {
        required: {
          message: "原密码：不能为空哟",
        },
      },
      pass2: {
        required: {
          message: "新密码：不能为空哟",
        },
      },
      pass3: {
        required: {
          message: "确认密码：不能为空哟",
        },
        equalTo: {
          field: "pass2",
          message: "新密码和确认密码不一致",
        },
      },
    });
  },

  //修改密码
  submitForm: function (e) {
    if (!this.WxValidate.checkForm(e)) {
      //输入验证不通过时，弹出相应的提示信息，并返回
      const error = this.WxValidate.errorList[0];
      var msg = `${error.msg}`;
      App.showToast(msg, "none");
      return false;
    } else {
      var data = e.detail.value;
      data = App.Tools.extend(data, {
        f: 1,
        lname: wx.getStorageSync("lname"),
        by1: data.pass1,
        by2: data.pass2,
      });

      App.HttpService.saveData(data, "/users/updatePwd2.action").then((data) => {
        //执行服务器Servlet，并根据返回值，弹出相应的提示
        if (data == "1") {
          App.showToast("原密码错误", "none");
        } else {
          App.WxService.showToast({
            title: "修改成功!",
            icon: "success",
            duration: 1500,
          });
          setTimeout(function () {
            wx.navigateBack(); //返回上页
          }, 1500);
        }
      });
    }
  },
  resetForm: function () {
    console.log("form发生了reset事件");
  },
});
