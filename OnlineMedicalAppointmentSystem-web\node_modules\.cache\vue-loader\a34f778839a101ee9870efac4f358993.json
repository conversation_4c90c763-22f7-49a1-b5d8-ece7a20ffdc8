{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total2.vue?vue&type=template&id=75fa3093", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total2.vue", "mtime": 1749221024000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogICAgPGRpdiBzdHlsZT0id2lkdGg6IDEwMCU7bGluZS1oZWlnaHQ6IDMwcHg7dGV4dC1hbGlnbjogbGVmdDsiPg0KICAgICAgIDxkaXYgaWQ9ImNvbnRhaW5lciIgOnN0eWxlPSJ7IGhlaWdodDogJzYwMHB4JyB9Ij48L2Rpdj4KIA0KICAgIDwvZGl2Pg0K"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total2.vue"], "names": [], "mappings": ";IACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACzD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEzD,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/admin/total/Total2.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <div id=\"container\" :style=\"{ height: '600px' }\"></div>\n \r\n    </div>\r\n</template>\r\n\r\n<script>\r\n\n import request, { base } from \"../../../../utils/http\";\n import * as echarts from 'echarts';\n \n export default {\n   data() {\n     return {\n       pieData: [],\n       pieName: [],\n     };\n   },\n \n   mounted() {\n     this.getdata();\n     window.addEventListener('resize', this.resizeChart);\n   },\n   beforeDestroy() {\n     window.removeEventListener('resize', this.resizeChart);\n   },\n   methods: {\n     // 数据初始化\n     getdata() {\n       let url = base + \"/ReportData/queryReport2\";\n \n       let para = {};\n \n       request.post(url, para).then((res) => {\n         if (res.code == 200) {\n           var ss = res.resdata;\n           var pieName2 = [];\n           var pieData2 = [];\n           for (let i = 0; i < ss.length; i++) {\n             pieName2[i] = ss[i].name;\n             pieData2[i] = ss[i].num;\n           }\n           this.pieName = pieName2;\n           this.pieData = pieData2;\n           this.initEcharts();\n         } else {\n           this.$message.error(res.msg);\n         }\n       });\n     },\n \n     initEcharts() {\n       const dom = document.getElementById('container');\n       const myChart = echarts.init(dom, null, { renderer: 'canvas' });\n \n       const option = {\n         title: {\n \n         },\n         tooltip: {\n           trigger: 'axis',\n           axisPointer: {\n             type: 'shadow'\n           }\n         },\n         legend: {},\n         grid: {\n           left: '3%',\n           right: '4%',\n           bottom: '3%',\n           containLabel: true\n         },\n         xAxis: {\n           type: 'value',\n           boundaryGap: [0, 0.01]\n         },\n         yAxis: {\n           type: 'category',\n           data: this.pieName\n         },\n         series: [\n           {\n             name: '总销售数量',\n             type: 'bar',\n             data: this.pieData\n           }\n         ]\n       };\n \n       if (option && typeof option === 'object') {\n         myChart.setOption(option);\n       }\n     },\n     resizeChart() {\n       const dom = document.getElementById('container');\n       const myChart = echarts.getInstanceByDom(dom);\n       myChart.resize();\n     }\n   }\n };\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"]}]}