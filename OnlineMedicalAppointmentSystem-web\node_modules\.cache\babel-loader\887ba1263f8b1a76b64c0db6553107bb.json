{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\doctor\\DoctorDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\doctor\\DoctorDetail.vue", "mtime": 1749208951193}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uLy4uL3V0aWxzL2h0dHAiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0RvY3RvckRldGFpbCcsCiAgY29tcG9uZW50czoge30sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGlkOiAnJywKICAgICAgZm9ybURhdGE6IHt9IC8v6KGo5Y2V5pWw5o2uICAgICAgICAgCiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuaWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5pZDsgLy/ojrflj5blj4LmlbAKICAgIHRoaXMuZ2V0RGF0YXMoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8v6I635Y+W5YiX6KGo5pWw5o2uCiAgICBnZXREYXRhcygpIHsKICAgICAgbGV0IHBhcmEgPSB7fTsKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7CiAgICAgIGxldCB1cmwgPSBiYXNlICsgIi9kb2N0b3IvZ2V0P2lkPSIgKyB0aGlzLmlkOwogICAgICByZXF1ZXN0LnBvc3QodXJsLCBwYXJhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5mb3JtRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocmVzLnJlc2RhdGEpKTsKICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOi/lOWbngogICAgYmFjaygpIHsKICAgICAgLy/ov5Tlm57kuIrkuIDpobUKICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "id", "formData", "created", "$route", "query", "getDatas", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata", "back", "$router", "go"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\doctor\\DoctorDetail.vue"], "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n        <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n            <el-form-item label=\"医生ID\">\r\n                {{ formData.did }}</el-form-item>\r\n            <el-form-item label=\"账号\">\r\n                {{ formData.daccount }}</el-form-item>\r\n            <el-form-item label=\"登录密码\">\r\n                {{ formData.password }}</el-form-item>\r\n            <el-form-item label=\"姓名\">\r\n                {{ formData.dname }}</el-form-item>\r\n            <el-form-item label=\"性别\">\r\n                {{ formData.sex }}</el-form-item>\r\n            <el-form-item label=\"照片\" prop=\"photo\">\r\n                <img :src=\"'http://localhost:8088/OnlineMedicalAppointmentSystem_Server/upload/' + formData.photo\"\r\n                    style=\"width: 150px;height: 150px\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"职称\">\r\n                {{ formData.jobs }}</el-form-item>\r\n            <el-form-item label=\"联系方式\">\r\n                {{ formData.tel }}</el-form-item>\r\n            <el-form-item label=\"擅长领域\">\r\n                {{ formData.shac }}</el-form-item>\r\n            <el-form-item label=\"挂号费\">\r\n                {{ formData.price }}</el-form-item>\r\n            <el-form-item label=\"科室\">\r\n                {{ formData.pname }}</el-form-item>\r\n            <el-form-item label=\"医生简介\">\r\n                {{ formData.dmemo }}</el-form-item>\r\n            <el-form-item label=\"添加时间\">\r\n                {{ formData.addtime }}</el-form-item>\r\n            <el-form-item>\r\n                <el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport request, { base } from \"../../../../utils/http\";\r\nexport default {\r\n    name: 'DoctorDetail',\r\n    components: {\r\n    },\r\n    data() {\r\n        return {\r\n            id: '',\r\n            formData: {}, //表单数据         \r\n\r\n        };\r\n    },\r\n    created() {\r\n        this.id = this.$route.query.id; //获取参数\r\n        this.getDatas();\r\n    },\r\n\r\n\r\n    methods: {\r\n\r\n        //获取列表数据\r\n        getDatas() {\r\n            let para = {\r\n            };\r\n            this.listLoading = true;\r\n            let url = base + \"/doctor/get?id=\" + this.id;\r\n            request.post(url, para).then((res) => {\r\n                this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n                this.listLoading = false;\r\n            });\r\n        },\r\n\r\n        // 返回\r\n        back() {\r\n            //返回上一页\r\n            this.$router.go(-1);\r\n        },\r\n\r\n    },\r\n}\r\n\r\n</script>\r\n<style scoped></style>\r\n"], "mappings": "AA0CA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACXC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE,CACZ,CAAC;EACDC,IAAIA,CAAA,EAAG;IACH,OAAO;MACHC,EAAE,EAAE,EAAE;MACNC,QAAQ,EAAE,CAAC,CAAC,CAAE;IAElB,CAAC;EACL,CAAC;EACDC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,EAAC,GAAI,IAAI,CAACG,MAAM,CAACC,KAAK,CAACJ,EAAE,EAAE;IAChC,IAAI,CAACK,QAAQ,CAAC,CAAC;EACnB,CAAC;EAGDC,OAAO,EAAE;IAEL;IACAD,QAAQA,CAAA,EAAG;MACP,IAAIE,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIb,IAAG,GAAI,iBAAgB,GAAI,IAAI,CAACI,EAAE;MAC5CL,OAAO,CAACe,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAI,CAACX,QAAO,GAAIY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;MAC5B,CAAC,CAAC;IACN,CAAC;IAED;IACAS,IAAIA,CAAA,EAAG;MACH;MACA,IAAI,CAACC,OAAO,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC;IACvB;EAEJ;AACJ", "ignoreList": []}]}