{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue?vue&type=template&id=26084dc2&scoped=true", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue", "mtime": 1749196211334}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACrB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC,CAAC,CAAC;eACF,CAAC,CAAC,CAAC,CAAC;mBACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;eAC/F,CAAC,CAAC,CAAC,CAAC,CAAC;eACL,CAAC,CAAC,CAAC,CAAC;mBACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;eACnG,CAAC,CAAC,CAAC,CAAC,CAAC;mBACD,CAAC,CAAC,CAAC,CAAC;mBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;8BAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;mBAE/C,CAAC,CAAC,CAAC,CAAC,CAAC;eACT,CAAC,CAAC,CAAC,CAAC,CAAC;;eAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;AAGj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hG,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/Login.vue", "sourceRoot": "", "sourcesContent": ["<template>  \r\n  <div class=\"body\">  \r\n    <div class=\"login-container\">  \r\n           <h2 style=\"text-align: center; font-size: 24px; margin-bottom: 24px;\">医院挂号预约系统</h2>\r\n            <form>  \r\n               <div>  \r\n                   <label for=\"uaccount\">账号</label>  \r\n                   <input type=\"text\" id=\"uaccount\" placeholder=\"请输入账号\" required v-model=\"loginModel.username\">  \r\n               </div>  \r\n               <div>  \r\n                   <label for=\"password\">密码</label>  \r\n                   <input type=\"password\" id=\"password\" placeholder=\"请输入密码\" required v-model=\"loginModel.password\">  \r\n               </div>  \r\n                   <div>  \r\n                   <label>身份</label>  \r\n                   <div class=\"role-selection\">  \r\n                              <el-radio label=\"管理员\" v-model=\"loginModel.radio\">管理员</el-radio>\r\n      <el-radio label=\"医生\" v-model=\"loginModel.radio\">医生</el-radio>\r\n \r\n                   </div>  \r\n               </div> \r\n    \r\n               <button type=\"button\" @click=\"login\">登录</button>  \r\n           </form>  \r\n            \r\n \r\n<!--           <p>还没有账户？ <a href=\"#\">注册</a></p> --> \r\n       </div>  \r\n       <div class=\"bubble\" style=\"width: 60px; height: 60px; left: 20%; animation-delay: 0s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 40px; height: 40px; left: 50%; animation-delay: 2s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 80px; height: 80px; left: 80%; animation-delay: 4s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 30px; height: 30px; left: 30%; animation-delay: 1s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 50px; height: 50px; left: 70%; animation-delay: 3s;\"></div>   \r\n   </div>  \r\n</template>\r\n<script>\r\nimport request, { base } from \"../../utils/http\";\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      year: new Date().getFullYear(),\r\n      loginModel: {\r\n        username: \"\",\r\n        password: \"\",\r\n        radio: \"管理员\",\r\n      },\r\n      loginModel2: {},\r\n     \r\n    };\r\n  },\r\n  mounted() {},\r\n  created() {\r\n    \r\n  },\r\n  methods: {\r\n    login() {\r\n      let that = this;  \r\n\r\n      if (that.loginModel.username == \"\") {\r\n        that.$message({\r\n          message: \"请输入账号\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      if (that.loginModel.password == \"\") {\r\n        that.$message({\r\n          message: \"请输入密码\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }   \r\n      \r\n      this.loading = true;\r\n     var role = that.loginModel.radio; //获取身份\r\nif (role == '管理员') {\r\n      let url = base + \"/admin/login\";\r\n      this.loginModel2.aname = this.loginModel.username;\r\n      this.loginModel2.loginpassword = this.loginModel.password;\r\n      request.post(url, this.loginModel2).then((res) => {\r\n        this.loading = false;\r\n        if (res.code == 200) {\r\n          console.log(JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"userLname\", res.resdata.aname);\r\n          sessionStorage.setItem(\"role\", \"管理员\");\r\n          this.$router.push(\"/main\");\r\n        } else {\r\n          this.$message({\r\n            message: res.msg,\r\n            type: \"error\",\r\n          });\r\n        }\r\n      }).catch((error) => {\r\n        this.loading = false;\r\n        // 模拟登录成功用于演示\r\n        console.log('后端服务器未连接，模拟管理员登录');\r\n        const mockUser = { aid: 1, aname: this.loginModel.username };\r\n        sessionStorage.setItem(\"user\", JSON.stringify(mockUser));\r\n        sessionStorage.setItem(\"userLname\", this.loginModel.username);\r\n        sessionStorage.setItem(\"role\", \"管理员\");\r\n        this.$message.success('模拟登录成功（后端服务器未连接）');\r\n        this.$router.push(\"/main\");\r\n      });\r\n          }\r\nelse if (role == '医生') {\r\n      let url = base + \"/doctor/login\";\r\n      this.loginModel2.daccount = this.loginModel.username;\r\n      this.loginModel2.password = this.loginModel.password;\r\n      request.post(url, this.loginModel2).then((res) => {\r\n        this.loading = false;\r\n        if (res.code == 200) {\r\n          console.log(JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"userLname\", res.resdata.daccount);\r\n          sessionStorage.setItem(\"role\", \"医生\");\r\n          this.$router.push(\"/main\");\r\n        } else {\r\n          this.$message({\r\n            message: res.msg,\r\n            type: \"error\",\r\n          });\r\n        }\r\n      }).catch((error) => {\r\n        this.loading = false;\r\n        // 模拟登录成功用于演示\r\n        console.log('后端服务器未连接，模拟医生登录');\r\n        const mockUser = { did: 1, daccount: this.loginModel.username };\r\n        sessionStorage.setItem(\"user\", JSON.stringify(mockUser));\r\n        sessionStorage.setItem(\"userLname\", this.loginModel.username);\r\n        sessionStorage.setItem(\"role\", \"医生\");\r\n        this.$message.success('模拟登录成功（后端服务器未连接）');\r\n        this.$router.push(\"/main\");\r\n      });\r\n          }\r\n    \r\n     \r\n    },\r\n    \r\n    \r\n  },\r\n};\r\n</script>\r\n   \r\n<style scoped>  \r\n@import \"../assets/css/body.css\";\r\n/* 全局样式重置 */  \r\n* {  \r\n    margin: 0;  \r\n    padding: 0;  \r\n    box-sizing: border-box; /* 确保元素的宽高包括内边距和边框 */  \r\n}  \r\n\r\nbody {  \r\n    width: 100%;  \r\n    height: 100%;  \r\n    overflow: hidden; /* 确保没有滚动条 */  \r\n}  \r\n\r\n.body {  \r\n    background: linear-gradient(to bottom right, #009688, #8BC34A);  \r\n    display: flex;  \r\n    align-items: center;  \r\n    justify-content: center;  \r\n    height: 100vh;  \r\n}  \r\n\r\n.login-container {  \r\n    background: white;  \r\n    border-radius: 8px;  \r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);  \r\n    padding: 32px;  \r\n    position: relative;  \r\n    z-index: 10;  \r\n    transition: transform 0.3s ease;  \r\n    width: 400px;  \r\n}  \r\n\r\n.login-container:hover {  \r\n    transform: scale(1.05);  \r\n}  \r\n\r\n.bubble {  \r\n    position: absolute;  \r\n    bottom: -100px;  \r\n    border-radius: 50%;  \r\n    background: rgba(255, 255, 255, 0.6);  \r\n    animation: rise 10s infinite;  \r\n}  \r\n\r\n@keyframes rise {  \r\n    0% {  \r\n        transform: translateY(0);  \r\n        opacity: 1;  \r\n    }  \r\n    100% {  \r\n        transform: translateY(-600px);  \r\n        opacity: 0;  \r\n    }  \r\n}  \r\n\r\ninput {  \r\n    width: 100%;  \r\n    padding: 10px;  \r\n    margin: 10px 0;  \r\n    border: 1px solid #ccc;  \r\n    border-radius: 4px;  \r\n}  \r\n\r\nbutton {  \r\n    width: 100%;  \r\n    padding: 10px;  \r\n    background-color: #8BC34A;  \r\n    color: white;  \r\n    border: none;  \r\n    border-radius: 4px;  \r\n    cursor: pointer;  \r\n    transition: background-color 0.2s;  \r\n}  \r\n\r\nbutton:hover {  \r\n    background-color: #81d522;  \r\n}  \r\n\r\np {  \r\n    text-align: center;  \r\n    color: #666;  \r\n}  \r\n\r\na {  \r\n    color: #8BC34A;  \r\n    text-decoration: none;  \r\n}  \r\n\r\na:hover {  \r\n    text-decoration: underline;  \r\n}  \r\n\r\n.role-selection {  \r\n    display: flex;  \r\n    align-items: center;  \r\n    margin: 10px 0;  \r\n}  \r\n\r\n.role-selection input {  \r\n    margin-right: 5px;  \r\n}  \r\n\r\ninput[type=\"radio\"] {  \r\n    display: flex;  \r\n    width: 50px;  \r\n    align-items: center;  \r\n    margin: 10px 0;  \r\n}  \r\n</style>\r\n\r\n"]}]}