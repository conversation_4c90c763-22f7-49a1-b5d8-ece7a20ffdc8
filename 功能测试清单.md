# 医院挂号系统统计报表功能测试清单

## 测试前准备

### 1. 环境检查
- [ ] 数据库服务正常运行
- [ ] 后端Spring服务正常启动
- [ ] 前端Vue服务正常运行
- [ ] 浏览器支持现代JavaScript特性

### 2. 数据准备
- [ ] 科室表(parts)有数据
- [ ] 医生表(doctor)有数据
- [ ] 预约表(reserve)有数据
- [ ] 用户表(users)有数据
- [ ] 管理员账号可正常登录
- [ ] 医生账号可正常登录

### 3. 依赖安装
```bash
cd OnlineMedicalAppointmentSystem-web
npm install
```
确认新增依赖已安装：
- [ ] xlsx@^0.18.5
- [ ] file-saver@^2.0.5

## 功能测试

### 管理员功能测试

#### 1. 科室预约统计（饼图）
- [ ] 管理员登录后可看到"统计报表"菜单
- [ ] 点击"科室预约统计"可正常跳转
- [ ] 页面显示饼图
- [ ] 图表标题显示"科室预约统计"
- [ ] 图表数据正确（各科室预约数量）
- [ ] 鼠标悬停显示详细信息
- [ ] 点击"导出Excel"按钮
- [ ] 成功下载Excel文件
- [ ] Excel文件包含：序号、科室名称、预约数量、占比

#### 2. 医生预约统计（柱状图）
- [ ] 点击"医生预约统计"可正常跳转
- [ ] 页面显示柱状图
- [ ] 图表标题显示"医生预约统计"
- [ ] 显示TOP 10医生数据
- [ ] 鼠标悬停显示详细信息
- [ ] 点击"导出Excel"按钮
- [ ] 成功下载Excel文件
- [ ] Excel文件包含：序号、医生姓名、预约数量

#### 3. 日预约统计（折线图）
- [ ] 点击"日预约统计"可正常跳转
- [ ] 页面显示折线图
- [ ] 图表标题显示"日预约统计"
- [ ] 显示最近30天数据
- [ ] 折线图平滑显示
- [ ] 有面积填充效果
- [ ] 点击"导出Excel"按钮
- [ ] 成功下载Excel文件
- [ ] Excel文件包含：序号、日期、预约数量

#### 4. 月预约统计（柱状图）
- [ ] 点击"月预约统计"可正常跳转
- [ ] 页面显示柱状图
- [ ] 图表标题显示"月预约统计"
- [ ] 显示最近12个月数据
- [ ] 柱状图颜色为绿色
- [ ] 点击"导出Excel"按钮
- [ ] 成功下载Excel文件
- [ ] Excel文件包含：序号、月份、预约数量

### 医生功能测试

#### 1. 菜单权限检查
- [ ] 医生登录后可看到"统计报表"菜单
- [ ] 菜单中只显示"日预约统计"和"月预约统计"
- [ ] 不显示"科室预约统计"和"医生预约统计"

#### 2. 我的日预约统计
- [ ] 点击"日预约统计"可正常跳转
- [ ] 页面显示折线图
- [ ] 图表标题显示"我的日预约统计"
- [ ] 只显示当前医生的数据
- [ ] 显示最近30天数据
- [ ] 点击"导出Excel"按钮
- [ ] Excel文件名包含"我的日预约统计"
- [ ] 数据只包含当前医生的预约

#### 3. 我的月预约统计
- [ ] 点击"月预约统计"可正常跳转
- [ ] 页面显示柱状图
- [ ] 图表标题显示"我的月预约统计"
- [ ] 只显示当前医生的数据
- [ ] 显示最近12个月数据
- [ ] 点击"导出Excel"按钮
- [ ] Excel文件名包含"我的月预约统计"
- [ ] 数据只包含当前医生的预约

## 异常情况测试

### 1. 网络异常
- [ ] 后端服务停止时，前端显示错误提示
- [ ] 网络超时时，显示适当的错误信息
- [ ] 数据加载失败时，不显示空白图表

### 2. 数据异常
- [ ] 无数据时，图表显示空状态
- [ ] 数据格式错误时，显示错误提示
- [ ] 部分数据缺失时，图表正常显示

### 3. 权限异常
- [ ] 未登录用户无法访问统计页面
- [ ] 医生无法访问管理员专用统计
- [ ] 错误的医生ID不会显示其他医生数据

## 性能测试

### 1. 加载性能
- [ ] 图表加载时间 < 3秒
- [ ] Excel导出时间 < 5秒
- [ ] 大数据量时图表渲染正常

### 2. 响应式测试
- [ ] 手机端图表显示正常
- [ ] 平板端图表显示正常
- [ ] 窗口大小改变时图表自适应

## 浏览器兼容性测试

- [ ] Chrome (推荐)
- [ ] Firefox
- [ ] Safari
- [ ] Edge

## API接口测试

### 1. 后端接口测试
使用Postman或类似工具测试：

#### 科室预约统计
```
POST /ReportData/queryReport
Content-Type: application/json
{}
```

#### 医生预约统计
```
POST /ReportData/queryReport2
Content-Type: application/json
{}
```

#### 日预约统计
```
POST /ReportData/queryReport3
Content-Type: application/json
{"by1": "1"}  // 医生ID，可选
```

#### 月预约统计
```
POST /ReportData/queryReport4
Content-Type: application/json
{"by1": "1"}  // 医生ID，可选
```

### 2. 响应格式检查
- [ ] 返回状态码200
- [ ] 响应格式为JSON
- [ ] 包含code、msg、resdata字段
- [ ] resdata为数组，包含name和num字段

## 问题记录

| 问题描述 | 严重程度 | 状态 | 备注 |
|---------|---------|------|------|
|         |         |      |      |

## 测试结论

- [ ] 所有功能正常
- [ ] 存在问题但不影响使用
- [ ] 存在严重问题需要修复

测试人员：___________
测试日期：___________
测试环境：___________
