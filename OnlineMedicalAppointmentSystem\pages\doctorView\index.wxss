.table {
  border: 1rpx solid #ccc;
  width: 100%;
}

.tr {
  display: flex;
  width: 100%;
  border-bottom: 1rpx solid #ccc;
}

.th,
.td {
  padding: 20rpx 10rpx;
  text-align: center;
  width: 100%;
  border-right: 1rpx solid #ccc;
}

.header {
  background: #f8f8f8;
  font-weight: bold;
}

.time-col {
  width: 180rpx;
  background: #f8f8f8;
}

.schedule-cell {
  color: #67C23A;
  font-size: 28rpx;
  padding: 10rpx;
}

.no-schedule {
  color: #999;
  font-size: 28rpx;
  padding: 10rpx;
}

.th {
  font-size: 28rpx;
}

.schedule-container {
  width: 100%;
  overflow-x: auto;
}

.schedule-header,
.schedule-row {
  display: flex;
  min-width: 700rpx;
}

.time-column,
.time-cell {
  width: 180rpx;
  flex-shrink: 0;
  text-align: center;
  padding: 20rpx 0;
  background-color: #f8f8f8;
  border-right: 1px solid #eee;
}

.date-column,
.schedule-cell {
  flex: 1;
  min-width: 120rpx;
  text-align: center;
  padding: 20rpx 0;
  border-right: 1px solid #eee;
}

.schedule-header {
  font-weight: bold;
  background-color: #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 1;
}

.week-day {
  font-size: 24rpx;
  color: #666;
}

.schedule-cell {
  height: 100rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.has-schedule {
  background-color: #e6f7ff;
}

.status {
  font-size: 28rpx;
  color: #1890ff;
}

.status.unavailable {
  color: #999;
}

.remaining {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

/* 底部按钮样式 */
.footer-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  height: 80rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.footer-btn {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border-radius: 0;
  margin: 0;
  padding: 0;
}

.btn-chat {
  background-color: #07c160;
  color: #fff;
}

.footer-btn text {
  margin-right: 10rpx;
}