const App = getApp();
Page({
  data: {
    msgs1: [],
    msgs2: [],
    url: App.Config.fileBasePath,
  },

  //弹出提示信息
  showModal(message) {
    App.WxService.showModal({
      title: "友情提示",
      content: message,
      showCancel: !1,
    });
  },
  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
    this.getMsgs1();
  },

  //页面显示
  onShow() {
    this.init();
  },

  async init() {
    this.getMsgs2();
  },

  getMsgs1() {
    var that = this;
    //设置要传递的参数
    let param = {
      f: 2,
      pid: this.data.globalOption.id,
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/parts/list2.action?currentPage=1&pageSize=1").then((data) => {
      //执行服务器Servlet
      this.setData({
        msgs1: data.resdata,
      });
    });
  },

  getMsgs2() {
    var that = this;
    //设置要传递的参数
    let param = {
      f: 1,
      pid: this.data.globalOption.id,
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/doctor/list2.action?currentPage=1&pageSize=500").then((data) => {
      //执行服务器Servlet
      this.setData({
        msgs2: data.resdata,
      });
    });
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },
});
