﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <div class="echart" id="mychart" :style="myChartStyle"></div>
 
    </div>
</template>

<script>

 import request, { base } from "../../../../utils/http";
 import * as echarts from "echarts";
 
 export default {
   data() {
     return {
       myChart: {},
       pieData: [
       ],
       pieName: [],
       myChartStyle: { float: "left", width: "100%", height: "550px" } //图表样式
     };
   },
   mounted() {
     this.getdata();
   },
   methods: {
 
     //数据初始化
     getdata() {
       let url = base + "/ReportData/queryReport";
 
       let para = {
       };
 
       request.post(url, para).then((res) => {
         if (res.code == 200) {
           var ss = res.resdata;
 
           for (let i = 0; i < ss.length; i++) {
             this.pieData[i] = {
               name: ss[i].name,
               value: ss[i].num
             };
 
             this.initDate();
             this.initEcharts();
           }
         } else {
           this.$message.error(res.msg);
         }
       });
 
     },
 
 
     initDate() {
       for (let i = 0; i < this.pieData.length; i++) {
         this.pieName[i] = this.pieData[i].name;
       }
     },
     initEcharts() {
       // 饼图
       const option = {
         legend: {
           // 图例
           data: this.pieName,
           right: "10%",
           top: "10%",
           orient: "vertical"
         },
         title: {
 
           text: "产品分类销售统计",
           top: "10%",
           left: "center"
         },
         series: [
           {
             type: "pie",
             label: {
               show: true,
               formatter: "{b} : {c} ({d}%)" // b代表名称，c代表对应值，d代表百分比
             },
             radius: "30%", //饼图半径
             data: this.pieData
           }
         ]
       };
       console.log(this.seriesData);
       const optionFree = {
         xAxis: {},
         yAxis: {},
         series: [
           {
             data: this.seriesData,
             type: "line",
             smooth: true
           }
         ]
       };
       this.myChart = echarts.init(document.getElementById("mychart"));
       this.myChart.setOption(option);
       //随着屏幕大小调节图表
       window.addEventListener("resize", () => {
         this.myChart.resize();
       });
     }
   }
 };
</script>
<style scoped>
</style>
 

