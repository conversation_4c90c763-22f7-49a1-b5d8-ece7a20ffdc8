const App = getApp();
Page({
  data: {
    genderArray: ["男", "女"],
    genderIndex: 0,
  },

  // 性别选择器变化
  bindGenderChange(e) {
    this.setData({
      genderIndex: e.detail.value,
    });
  },

  // 提交表单
  submitForm(e) {
    const formData = e.detail.value;

    if (!formData.peoname || !formData.phone || !formData.age) {
      App.showToast("请填写完整信息", "none");
      return;
    }

    const data = {
      lname: wx.getStorageSync("lname"),
      peoname: formData.peoname,
      phone: formData.phone,
      gender: this.data.genderArray[this.data.genderIndex],
      age: parseInt(formData.age),
    };

    App.HttpService.saveData(data, "/patient/add.action").then((res) => {
      wx.showToast({
        title: "添加成功",
        icon: "success",
        duration: 1500,
      });
      setTimeout(() => {
        // 获取上一页实例
        const pages = getCurrentPages();
        const prevPage = pages[pages.length - 2];
        // 调用上一页的刷新方法
        if (prevPage && prevPage.getPatients) {
          prevPage.getPatients();
        }
        wx.navigateBack();
      }, 1500);
    });
  },
});
