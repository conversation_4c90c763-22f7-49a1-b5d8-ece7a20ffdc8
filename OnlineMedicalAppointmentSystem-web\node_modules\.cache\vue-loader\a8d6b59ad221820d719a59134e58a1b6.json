{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total2.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total2.vue", "mtime": 1749221024000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total2.vue"], "names": [], "mappings": ";;CASC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;CAElC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;OACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;KACb,CAAC;GACH,CAAC;;GAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACrD,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACxD,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACR,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;OAE3C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;OAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;SACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;WACnB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;WACjB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;WACjB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;aAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACzB;WACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACpB,EAAE,CAAC,CAAC,CAAC,EAAE;WACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9B;OACF,CAAC,CAAC;KACJ,CAAC;;KAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OAChD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;OAE/D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;SACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;SAEP,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;WACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACf;SACF,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SACV,CAAC,CAAC,CAAC,CAAC,EAAE;WACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;WACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;WACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;WACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACnB,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;WACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACvB,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;WACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACnB,CAAC;SACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;WACN;aACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;aACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;WACnB;SACF;OACF,CAAC;;OAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;SACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OAC3B;KACF,CAAC;KACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OAChD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAClB;GACF;CACF,CAAC", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/admin/total/Total2.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <div id=\"container\" :style=\"{ height: '600px' }\"></div>\n \r\n    </div>\r\n</template>\r\n\r\n<script>\r\n\n import request, { base } from \"../../../../utils/http\";\n import * as echarts from 'echarts';\n \n export default {\n   data() {\n     return {\n       pieData: [],\n       pieName: [],\n     };\n   },\n \n   mounted() {\n     this.getdata();\n     window.addEventListener('resize', this.resizeChart);\n   },\n   beforeDestroy() {\n     window.removeEventListener('resize', this.resizeChart);\n   },\n   methods: {\n     // 数据初始化\n     getdata() {\n       let url = base + \"/ReportData/queryReport2\";\n \n       let para = {};\n \n       request.post(url, para).then((res) => {\n         if (res.code == 200) {\n           var ss = res.resdata;\n           var pieName2 = [];\n           var pieData2 = [];\n           for (let i = 0; i < ss.length; i++) {\n             pieName2[i] = ss[i].name;\n             pieData2[i] = ss[i].num;\n           }\n           this.pieName = pieName2;\n           this.pieData = pieData2;\n           this.initEcharts();\n         } else {\n           this.$message.error(res.msg);\n         }\n       });\n     },\n \n     initEcharts() {\n       const dom = document.getElementById('container');\n       const myChart = echarts.init(dom, null, { renderer: 'canvas' });\n \n       const option = {\n         title: {\n \n         },\n         tooltip: {\n           trigger: 'axis',\n           axisPointer: {\n             type: 'shadow'\n           }\n         },\n         legend: {},\n         grid: {\n           left: '3%',\n           right: '4%',\n           bottom: '3%',\n           containLabel: true\n         },\n         xAxis: {\n           type: 'value',\n           boundaryGap: [0, 0.01]\n         },\n         yAxis: {\n           type: 'category',\n           data: this.pieName\n         },\n         series: [\n           {\n             name: '总销售数量',\n             type: 'bar',\n             data: this.pieData\n           }\n         ]\n       };\n \n       if (option && typeof option === 'object') {\n         myChart.setOption(option);\n       }\n     },\n     resizeChart() {\n       const dom = document.getElementById('container');\n       const myChart = echarts.getInstanceByDom(dom);\n       myChart.resize();\n     }\n   }\n };\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"]}]}