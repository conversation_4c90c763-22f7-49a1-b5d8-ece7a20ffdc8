<view class="container">
    <diy-navbar bgColor="green" isBack="{{true}}">
        <view slot="backText"> 返回 </view>
        <view slot="content"> 预约详情 </view>
    </diy-navbar>

    <block wx:for="{{msgs1}}" wx:key="k">
        <view class="flex flex-wrap igy-col-24 flex2-clz">
            <view class="text-center ">
                <image src="{{url}}{{item.by2}}" mode="aspectFit"></image>
            </view>

            <view class="grid col-2  diy-col-24 ">

                <view>科室：<text>{{item.pname}}</text></view>
                <view>医生：<text>{{item.by1}}</text></view>

                <view>预约日期：<text>{{item.rdate}}</text></view>
                <view>时间段：<text>{{item.rtime}}</text></view>
                <view>挂号费：<text>{{item.pmoney}}</text></view>

                <view>姓名：<text>{{msgs2[0].peoname}}</text></view>
                <view>手机号码：<text>{{msgs2[0].phone}}</text></view>
                <view>性别：<text>{{msgs2[0].gender}}</text></view>
                <view>年龄：<text>{{msgs2[0].age}}</text></view>

                <view>预约状态：<text>{{item.flag}}</text></view>

                <view>诊断结果：<rich-text wx:if="{{item.results}}" nodes="{{item.results}}"></rich-text></view>

            </view>
            <view class="grid col-1">
                <view>提交时间：<text>{{item.addtime}}</text></view>
            </view>
        </view>
    </block>


    <view class="clearfix"></view>
</view>