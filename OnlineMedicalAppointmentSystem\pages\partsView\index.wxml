<view class="container">
    <diy-navbar bgColor="green" isBack="{{true}}">
        <view slot="backText"> 返回 </view>
        <view slot="content"> 科室详情 </view>
    </diy-navbar>
    <view class="flex diy-col-24">
        <view class="padding bg-white diy-col-24" wx:for="{{msgs1}}" wx:key="k">
            <view class=" text-center  text-lg " style="line-height: 40px;">
                {{item.pname}}
            </view>

            <view style="line-height: 23px;">
                <rich-text nodes="{{item.pmemo}}"></rich-text>
            </view>
        </view>
    </view>


    <view class="clearfix"></view>
    <view class="diy-title flex green diy-col-24 title-clz">
        <view class="title font-normal">
            <text class="diy-icon-title"></text> 科室医生
        </view>
    </view>
    <view class="clearfix"></view>
    <block wx:for="{{msgs2}}" wx:key="k">
            <view class="clearfix"></view>
        <view class="flex diy-col-24 items-start flex-nowrap flex8-clz " bindtap="navigateTo" data-url="doctorView"
            data-id="{{item.did}}">
            <image src="{{url}}{{item.photo}}" class="diy-image diy-col-0 image3-clz"
                style="height: 100px !important; width: 100px"></image>
            <view class="flex flex-wrap diy-col-0 flex-direction-column flex12-clz">
                <view class="diy-col-24 diy-text-md"> {{item.dname}}
                    <text class="cu-tag  padding-left-sm bg-green">{{item.jobs}}</text>
                </view>
                <view class="flex flex-wrap diy-col-24 justify-between items-center flex16-clz text-cut">
                    <view class="flex diy-col-0 flex-nowrap flex18-clz">
                        <view class="diy-col-0 text19-clz">{{item.sex}} </view>
                        <view class="diy-col-0 text19-clz">{{item.pname}} </view>

                    </view>
                    <view class="diy-col-0 text22-clz text-price" style="color:red;border:0px;font-size: 22px;">
                        {{item.price}} </view>
                </view>
             
            </view>
        </view>
        <view class="clearfix"></view>
    </block>
    <view class="clearfix"></view>
</view>