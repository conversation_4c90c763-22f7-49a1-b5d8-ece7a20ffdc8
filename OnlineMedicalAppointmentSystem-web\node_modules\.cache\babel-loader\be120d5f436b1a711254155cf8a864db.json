{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue?vue&type=template&id=26084dc2&scoped=true", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue", "mtime": 1749196211334}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "style", "for", "type", "id", "placeholder", "required", "$data", "loginModel", "username", "$event", "password", "_hoisted_3", "_createVNode", "_component_el_radio", "label", "radio", "_cache", "onClick", "args", "$options", "login", "_createCommentVNode"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\Login.vue"], "sourcesContent": ["<template>  \r\n  <div class=\"body\">  \r\n    <div class=\"login-container\">  \r\n           <h2 style=\"text-align: center; font-size: 24px; margin-bottom: 24px;\">医院挂号预约系统</h2>\r\n            <form>  \r\n               <div>  \r\n                   <label for=\"uaccount\">账号</label>  \r\n                   <input type=\"text\" id=\"uaccount\" placeholder=\"请输入账号\" required v-model=\"loginModel.username\">  \r\n               </div>  \r\n               <div>  \r\n                   <label for=\"password\">密码</label>  \r\n                   <input type=\"password\" id=\"password\" placeholder=\"请输入密码\" required v-model=\"loginModel.password\">  \r\n               </div>  \r\n                   <div>  \r\n                   <label>身份</label>  \r\n                   <div class=\"role-selection\">  \r\n                              <el-radio label=\"管理员\" v-model=\"loginModel.radio\">管理员</el-radio>\r\n      <el-radio label=\"医生\" v-model=\"loginModel.radio\">医生</el-radio>\r\n \r\n                   </div>  \r\n               </div> \r\n    \r\n               <button type=\"button\" @click=\"login\">登录</button>  \r\n           </form>  \r\n            \r\n \r\n<!--           <p>还没有账户？ <a href=\"#\">注册</a></p> --> \r\n       </div>  \r\n       <div class=\"bubble\" style=\"width: 60px; height: 60px; left: 20%; animation-delay: 0s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 40px; height: 40px; left: 50%; animation-delay: 2s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 80px; height: 80px; left: 80%; animation-delay: 4s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 30px; height: 30px; left: 30%; animation-delay: 1s;\"></div>  \r\n       <div class=\"bubble\" style=\"width: 50px; height: 50px; left: 70%; animation-delay: 3s;\"></div>   \r\n   </div>  \r\n</template>\r\n<script>\r\nimport request, { base } from \"../../utils/http\";\r\nexport default {\r\n  name: \"Login\",\r\n  data() {\r\n    return {\r\n      year: new Date().getFullYear(),\r\n      loginModel: {\r\n        username: \"\",\r\n        password: \"\",\r\n        radio: \"管理员\",\r\n      },\r\n      loginModel2: {},\r\n     \r\n    };\r\n  },\r\n  mounted() {},\r\n  created() {\r\n    \r\n  },\r\n  methods: {\r\n    login() {\r\n      let that = this;  \r\n\r\n      if (that.loginModel.username == \"\") {\r\n        that.$message({\r\n          message: \"请输入账号\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      if (that.loginModel.password == \"\") {\r\n        that.$message({\r\n          message: \"请输入密码\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }   \r\n      \r\n      this.loading = true;\r\n     var role = that.loginModel.radio; //获取身份\r\nif (role == '管理员') {\r\n      let url = base + \"/admin/login\";\r\n      this.loginModel2.aname = this.loginModel.username;\r\n      this.loginModel2.loginpassword = this.loginModel.password;\r\n      request.post(url, this.loginModel2).then((res) => {\r\n        this.loading = false;\r\n        if (res.code == 200) {\r\n          console.log(JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"userLname\", res.resdata.aname);\r\n          sessionStorage.setItem(\"role\", \"管理员\");\r\n          this.$router.push(\"/main\");\r\n        } else {\r\n          this.$message({\r\n            message: res.msg,\r\n            type: \"error\",\r\n          });\r\n        }\r\n      }).catch((error) => {\r\n        this.loading = false;\r\n        // 模拟登录成功用于演示\r\n        console.log('后端服务器未连接，模拟管理员登录');\r\n        const mockUser = { aid: 1, aname: this.loginModel.username };\r\n        sessionStorage.setItem(\"user\", JSON.stringify(mockUser));\r\n        sessionStorage.setItem(\"userLname\", this.loginModel.username);\r\n        sessionStorage.setItem(\"role\", \"管理员\");\r\n        this.$message.success('模拟登录成功（后端服务器未连接）');\r\n        this.$router.push(\"/main\");\r\n      });\r\n          }\r\nelse if (role == '医生') {\r\n      let url = base + \"/doctor/login\";\r\n      this.loginModel2.daccount = this.loginModel.username;\r\n      this.loginModel2.password = this.loginModel.password;\r\n      request.post(url, this.loginModel2).then((res) => {\r\n        this.loading = false;\r\n        if (res.code == 200) {\r\n          console.log(JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"user\", JSON.stringify(res.resdata));\r\n          sessionStorage.setItem(\"userLname\", res.resdata.daccount);\r\n          sessionStorage.setItem(\"role\", \"医生\");\r\n          this.$router.push(\"/main\");\r\n        } else {\r\n          this.$message({\r\n            message: res.msg,\r\n            type: \"error\",\r\n          });\r\n        }\r\n      }).catch((error) => {\r\n        this.loading = false;\r\n        // 模拟登录成功用于演示\r\n        console.log('后端服务器未连接，模拟医生登录');\r\n        const mockUser = { did: 1, daccount: this.loginModel.username };\r\n        sessionStorage.setItem(\"user\", JSON.stringify(mockUser));\r\n        sessionStorage.setItem(\"userLname\", this.loginModel.username);\r\n        sessionStorage.setItem(\"role\", \"医生\");\r\n        this.$message.success('模拟登录成功（后端服务器未连接）');\r\n        this.$router.push(\"/main\");\r\n      });\r\n          }\r\n    \r\n     \r\n    },\r\n    \r\n    \r\n  },\r\n};\r\n</script>\r\n   \r\n<style scoped>  \r\n@import \"../assets/css/body.css\";\r\n/* 全局样式重置 */  \r\n* {  \r\n    margin: 0;  \r\n    padding: 0;  \r\n    box-sizing: border-box; /* 确保元素的宽高包括内边距和边框 */  \r\n}  \r\n\r\nbody {  \r\n    width: 100%;  \r\n    height: 100%;  \r\n    overflow: hidden; /* 确保没有滚动条 */  \r\n}  \r\n\r\n.body {  \r\n    background: linear-gradient(to bottom right, #009688, #8BC34A);  \r\n    display: flex;  \r\n    align-items: center;  \r\n    justify-content: center;  \r\n    height: 100vh;  \r\n}  \r\n\r\n.login-container {  \r\n    background: white;  \r\n    border-radius: 8px;  \r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);  \r\n    padding: 32px;  \r\n    position: relative;  \r\n    z-index: 10;  \r\n    transition: transform 0.3s ease;  \r\n    width: 400px;  \r\n}  \r\n\r\n.login-container:hover {  \r\n    transform: scale(1.05);  \r\n}  \r\n\r\n.bubble {  \r\n    position: absolute;  \r\n    bottom: -100px;  \r\n    border-radius: 50%;  \r\n    background: rgba(255, 255, 255, 0.6);  \r\n    animation: rise 10s infinite;  \r\n}  \r\n\r\n@keyframes rise {  \r\n    0% {  \r\n        transform: translateY(0);  \r\n        opacity: 1;  \r\n    }  \r\n    100% {  \r\n        transform: translateY(-600px);  \r\n        opacity: 0;  \r\n    }  \r\n}  \r\n\r\ninput {  \r\n    width: 100%;  \r\n    padding: 10px;  \r\n    margin: 10px 0;  \r\n    border: 1px solid #ccc;  \r\n    border-radius: 4px;  \r\n}  \r\n\r\nbutton {  \r\n    width: 100%;  \r\n    padding: 10px;  \r\n    background-color: #8BC34A;  \r\n    color: white;  \r\n    border: none;  \r\n    border-radius: 4px;  \r\n    cursor: pointer;  \r\n    transition: background-color 0.2s;  \r\n}  \r\n\r\nbutton:hover {  \r\n    background-color: #81d522;  \r\n}  \r\n\r\np {  \r\n    text-align: center;  \r\n    color: #666;  \r\n}  \r\n\r\na {  \r\n    color: #8BC34A;  \r\n    text-decoration: none;  \r\n}  \r\n\r\na:hover {  \r\n    text-decoration: underline;  \r\n}  \r\n\r\n.role-selection {  \r\n    display: flex;  \r\n    align-items: center;  \r\n    margin: 10px 0;  \r\n}  \r\n\r\n.role-selection input {  \r\n    margin-right: 5px;  \r\n}  \r\n\r\ninput[type=\"radio\"] {  \r\n    display: flex;  \r\n    width: 50px;  \r\n    align-items: center;  \r\n    margin: 10px 0;  \r\n}  \r\n</style>\r\n\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAiB;;EAaRA,KAAK,EAAC;AAAgB;;;uBAd5CC,mBAAA,CAgCO,OAhCPC,UAgCO,GA/BLC,mBAAA,CAyBS,OAzBTC,UAyBS,G,4BAxBFD,mBAAA,CAAmF;IAA/EE,KAAiE,EAAjE;MAAA;MAAA;MAAA;IAAA;EAAiE,GAAC,UAAQ,sBAC7EF,mBAAA,CAmBM,eAlBHA,mBAAA,CAGM,c,0BAFFA,mBAAA,CAAgC;IAAzBG,GAAG,EAAC;EAAU,GAAC,IAAE,sB,gBACxBH,mBAAA,CAA4F;IAArFI,IAAI,EAAC,MAAM;IAACC,EAAE,EAAC,UAAU;IAACC,WAAW,EAAC,OAAO;IAACC,QAAQ,EAAR,EAAQ;+DAAUC,KAAA,CAAAC,UAAU,CAACC,QAAQ,GAAAC,MAAA;iDAAnBH,KAAA,CAAAC,UAAU,CAACC,QAAQ,E,KAE9FV,mBAAA,CAGM,c,0BAFFA,mBAAA,CAAgC;IAAzBG,GAAG,EAAC;EAAU,GAAC,IAAE,sB,gBACxBH,mBAAA,CAAgG;IAAzFI,IAAI,EAAC,UAAU;IAACC,EAAE,EAAC,UAAU;IAACC,WAAW,EAAC,OAAO;IAACC,QAAQ,EAAR,EAAQ;+DAAUC,KAAA,CAAAC,UAAU,CAACG,QAAQ,GAAAD,MAAA;iDAAnBH,KAAA,CAAAC,UAAU,CAACG,QAAQ,E,KAE9FZ,mBAAA,CAOE,c,0BANFA,mBAAA,CAAiB,eAAV,IAAE,sBACTA,mBAAA,CAIM,OAJNa,UAIM,GAHKC,YAAA,CAA+DC,mBAAA;IAArDC,KAAK,EAAC,KAAK;gBAAUR,KAAA,CAAAC,UAAU,CAACQ,KAAK;+DAAhBT,KAAA,CAAAC,UAAU,CAACQ,KAAK,GAAAN,MAAA;;sBAAE,MAAGO,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;;qCAC5EJ,YAAA,CAA6DC,mBAAA;IAAnDC,KAAK,EAAC,IAAI;gBAAUR,KAAA,CAAAC,UAAU,CAACQ,KAAK;+DAAhBT,KAAA,CAAAC,UAAU,CAACQ,KAAK,GAAAN,MAAA;;sBAAE,MAAEO,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;yCAKzClB,mBAAA,CAAgD;IAAxCI,IAAI,EAAC,QAAQ;IAAEe,OAAK,EAAAD,MAAA,QAAAA,MAAA,UAAAE,IAAA,KAAEC,QAAA,CAAAC,KAAA,IAAAD,QAAA,CAAAC,KAAA,IAAAF,IAAA,CAAK;KAAE,IAAE,E,GAItDG,mBAAA,kDAAmD,C", "ignoreList": []}]}