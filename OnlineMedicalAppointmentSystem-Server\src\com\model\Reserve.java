package com.model;
import java.util.List;

/**
* (reserve)预约挂号实体类
*/
public class Reserve extends ComData{
	
	private static final long serialVersionUID = 842885755436736L;
	private Integer rid;    //预约ID
	private Integer pid;    //科室
	private String pname;
	private Integer did;    //医生ID
	private Integer plid;    //坐诊ID
	private String rdate;    //预约日期
	private String rtime;    //预约时间段
	private Object pmoney;    //挂号费
	private String lname;    //用户名
	private Integer peoid;    //就诊人id
	private String addtime;    //提交时间
	private String flag;    //预约状态
	private String results;    //诊断结果
	private Patient patient;

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public Integer getPid() {
		return pid;
	}

	public void setPid(Integer pid) {
		this.pid = pid;
	}

	public String getPname() {
		return pname;
	}

	public void setPname(String pname) {
		this.pname = pname;
	}

	public Integer getDid() {
		return did;
	}

	public void setDid(Integer did) {
		this.did = did;
	}

	public Integer getPlid() {
		return plid;
	}

	public void setPlid(Integer plid) {
		this.plid = plid;
	}

	public String getRdate() {
		return rdate;
	}

	public void setRdate(String rdate) {
		this.rdate = rdate;
	}

	public String getRtime() {
		return rtime;
	}

	public void setRtime(String rtime) {
		this.rtime = rtime;
	}

	public Object getPmoney() {
		return pmoney;
	}

	public void setPmoney(Object pmoney) {
		this.pmoney = pmoney;
	}

	public String getLname() {
		return lname;
	}

	public void setLname(String lname) {
		this.lname = lname;
	}

	public Integer getPeoid() {
		return peoid;
	}

	public void setPeoid(Integer peoid) {
		this.peoid = peoid;
	}

	public String getAddtime() {
		return addtime;
	}

	public void setAddtime(String addtime) {
		this.addtime = addtime;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public String getResults() {
		return results;
	}

	public void setResults(String results) {
		this.results = results;
	}

	public Patient getPatient() {
		return patient;
	}

	public void setPatient(Patient patient) {
		this.patient = patient;
	}

}

