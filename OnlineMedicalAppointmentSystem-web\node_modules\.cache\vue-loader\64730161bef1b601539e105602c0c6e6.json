{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\LeftMenu.vue?vue&type=style&index=0&id=edc10994&scoped=true&lang=css", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\LeftMenu.vue", "mtime": 1749221176434}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749193686285}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749193687363}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749193686702}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouc3ViLW1lbnUtaXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBhZGRpbmc6IDhweCAxNXB4Ow0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KfQ0KDQouc3ViLW1lbnUtaXRlbTpob3ZlciB7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDVweCk7DQp9DQoNCi5zdWItbWVudS1pdGVtIGkgew0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg=="}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\LeftMenu.vue"], "names": [], "mappings": ";AAudA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/components/LeftMenu.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n\r\n  <div class=\"deznav\" style=\"background:#8BC34A;\">\r\n    <div class=\"deznav-scroll mm-active\">\r\n      <ul class=\"metismenu mm-show\" id=\"menu\" v-show=\"role === '管理员'\">\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">患者管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/usersManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理患者</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">科室管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/partsAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加科室</router-link></li>\r\n            <li><router-link to=\"/partsManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理科室</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">医生管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/doctorAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加医生</router-link></li>\r\n            <li><router-link to=\"/doctorManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理医生</router-link>\r\n            </li>\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">排班管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/plansAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加排班</router-link></li>\r\n            <li><router-link to=\"/plansManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理排班</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">预约挂号管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/reserveManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理预约挂号</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">统计报表</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/total1\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>科室预约统计</router-link>\r\n            </li>\r\n                  <li><router-link to=\"/total2\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>医生预约统计</router-link>\r\n            </li>\r\n                  <li><router-link to=\"/total3\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>日预约统计</router-link>\r\n            </li>\r\n                  <li><router-link to=\"/total4\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>月预约统计</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">系统管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/mailremindertemplateEdit\" class=\"sub-menu-item\">\r\n                <i class=\"el-icon-right\"></i> 邮件模板设置\r\n              </router-link></li>\r\n            <li><router-link to=\"/password\" class=\"sub-menu-item\">\r\n                <i class=\"el-icon-right\"></i> 修改密码\r\n              </router-link></li>\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n      </ul>\r\n\r\n      <ul class=\"metismenu mm-show\" id=\"menu\" v-show=\"role === '医生'\">\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">排班管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/plansAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加排班</router-link></li>\r\n            <li><router-link to=\"/plansManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理排班</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">聊天管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/chatinfoManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理聊天</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">医生管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/doctorAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加医生</router-link></li>\r\n            <li><router-link to=\"/doctorManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理医生</router-link>\r\n            </li>\r\n            <li><router-link to=\"/doctorInfo\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>修改个人信息</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">患者管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/usersManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理患者</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">预约挂号管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/reserveManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理预约挂号</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n   \r\n\r\n                <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">统计报表</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n         \r\n              \r\n                  <li><router-link to=\"/total3\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>日预约统计</router-link>\r\n            </li>\r\n                  <li><router-link to=\"/total4\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>月预约统计</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">系统管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/password\" class=\"sub-menu-item\">\r\n                <i class=\"el-icon-right\"></i> 修改密码\r\n              </router-link></li>\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n      </ul>\r\n\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport $ from 'jquery';\r\n\r\nexport default {\r\n  name: \"LeftMenu\",\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      activeMenu: null, // 用于跟踪当前激活的菜单\r\n      showexist: false,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n\r\n    // 修改菜单点击事件\r\n    $('.has-arrow').click(function (e) {\r\n      e.preventDefault();\r\n\r\n      // 为父li元素添加mm-active类\r\n      $(this).parent('li').addClass('mm-active');\r\n\r\n      $(this).next('ul').toggleClass('mm-show');\r\n\r\n      // 关闭其他打开的菜单\r\n      $('.has-arrow').not(this).parent('li').removeClass('mm-active');\r\n      $('.has-arrow').not(this).next('ul').removeClass('mm-show');\r\n    });\r\n\r\n\r\n  },\r\n  methods: {\r\n\r\n    toggleShowExist() {\r\n      this.showexist = !this.showexist;\r\n\r\n      if (this.showexist) {\r\n        $(\".dropdown-menu\").removeClass(\"show\");\r\n      } else {\r\n        $(\".dropdown-menu\").addClass(\"show\");\r\n      }\r\n\r\n    },\r\n\r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          sessionStorage.removeItem(\"userLname\");\r\n          sessionStorage.removeItem(\"role\");\r\n          _this.$router.push(\"/\");\r\n        })\r\n        .catch(() => { });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.sub-menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 15px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sub-menu-item:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  transform: translateX(5px);\r\n}\r\n\r\n.sub-menu-item i {\r\n  margin-right: 10px;\r\n  font-size: 14px;\r\n}\r\n</style>\r\n"]}]}