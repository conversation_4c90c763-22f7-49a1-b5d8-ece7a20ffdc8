<view class="container">
    <diy-navbar bgColor="green">
        <view slot="content">首页</view>
    </diy-navbar>
    <view class="diy-col-24">
        <view class="diy-search">
            <view class="flex1 flex padding-xs solid radius">
                <text style="color: #555 !important" class="diy-icon-search"></text>
                <input class="flex1" name="search" type="" value="{{keys}}" data-key="search" bindchange="changeValue"
                    placeholder="请输入科室名称或医生名称" />
            </view>
            <view style="color: #ffffff !important" class="cu-tag margin-left-xs radius-xs green" bind:tap="search"> 搜索
            </view>
        </view>
    </view>

    <view class="flex diy-col-24">
        <swiper class="swiper" indicator-color="rgba(51, 51, 51, 0.39)" indicator-active-color="#fff"
            indicator-dots="true" autoplay="true" interval="3000" circular="true" style="height: 190px">
            <swiper-item class="diy-swiper-item">
                <view class="diy-swiper-item-wrap">
                    <image src="/assets/images/pic1.jpg" class="diy-swiper-image"></image>

                </view>
            </swiper-item>
            <swiper-item class="diy-swiper-item">
                <view class="diy-swiper-item-wrap">
                    <image src="/assets/images/pic2.jpg" class="diy-swiper-image"></image>

                </view>
            </swiper-item>
            <swiper-item class="diy-swiper-item">
                <view class="diy-swiper-item-wrap">
                    <image src="/assets/images/pic3.jpg" class="diy-swiper-image"></image>

                </view>
            </swiper-item>

        </swiper>
    </view>
    <view class="clearfix"></view>



    <view class="clearfix"></view>
    <view class="diy-title flex green diy-col-24 title-clz" catchtap="navigateTo" data-url="partsList">
        <view class="title font-normal"> <text class="diy-icon-title"></text> 科室 </view>
        <view class="more">
            更多
            <text class="diy-icon-right"></text>
        </view>
    </view>
    <view class="clearfix"></view>
    <block wx:for="{{msgs1}}" wx:key="k">
        <view class="grid" bindtap="navigateTo" data-url="partsView" data-id="{{item.pid}}">
            <view class="diy-col-24" style="font-size: 16px">{{item.pname}}</view>
            <view class="diy-col-24">{{item.pmemo}}
                <text class="cu-tag bg-cyan round ">查看详情</text>
            </view>
        </view>
    </block>


    <!-- 推荐医生 -->
    <view class="doctor-section">
        <view class="section-title">热门医生</view>
        <view class="doctor-list">
            <view class="doctor-item" wx:for="{{msgs2}}" wx:key="did" bindtap="goDoctorDetail" data-id="{{item.did}}">
                <image class="doctor-avatar" src="{{url}}{{item.photo}}" mode="aspectFill"></image>
                <view class="doctor-info">
                    <view class="doctor-name">{{item.dname}}</view>
                    <view class="doctor-title">{{item.jobs}}</view>
                    <view class="doctor-dept">{{item.pname}}</view>
                </view>
                <view class="doctor-action">
                    <text class="price">￥{{item.price}}</text>
                    <view class="book-btn">预约</view>
                </view>
            </view>
        </view>
    </view>

    <view class="clearfix"></view>
</view>