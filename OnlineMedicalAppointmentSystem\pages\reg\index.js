const App = getApp();
Page({
  data: {
    files2: [],
    filestr2: "",
   url: App.Config.fileBasePath,

    radioDatas1: [
      { value: "男", label: "男", checked: "true" },
      { value: "女", label: "女" },
    ],
    radioLabel1: "男",
  },

  //弹出提示信息
  showModal(message) {
    App.WxService.showModal({
      title: "友情提示",
      content: message,
      showCancel: !1,
    });
  },
  onShareAppMessage: function () { },

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
  },

  //页面显示
  onShow() {
    this.init();
  },

  async init() {
    this.getForm1();
  },

  //设置输入验证
  getForm1() {
    this.WxValidate = App.WxValidate({
      lname: {
        required: {
          message: "用户名：不能为空哟",
        },
      },
      upassword: {
        required: {
          message: "登录密码：不能为空哟",
        },
      },
      upassword2: {
        required: {
          message: "确认密码：不能为空哟",
        },
        equalTo: {
          field: "upassword",
          message: "密码和确认密码不一致",
        },
      },
      uname: {
        required: {
          message: "姓名：不能为空哟",
        },
      },
      uphone: {
        tel: {
          message: "手机号码：请输入11位的手机号码",
        },
        required: {
          message: "手机号码：不能为空哟",
        },
      },
      address: {
        required: {
          message: "家庭地址：不能为空哟",
        },
      },
    });
  },

  //注册
  submitForm: function (e) {
    if (!this.WxValidate.checkForm(e)) {
      //输入验证不通过时，弹出相应的提示信息，并返回
      const error = this.WxValidate.errorList[0];
      var msg = `${error.msg}`;
      App.showToast(msg, "none");
      return false;
    } else if (this.data.filestr2 == "") {
      App.showToast("请上传照片", "none");
      return false;
    } else {
      var data = e.detail.value;
      data.pic = this.data.filestr2;

 
      App.HttpService.saveData(data, "/users/add2.action").then((data) => {
        //执行服务器端，并根据返回值，弹出相应的提示
        if (data.code == 200) {
          App.WxService.showToast({
            title: "注册成功，请登录!",
            icon: "none",
            duration: 1500,
          });
          setTimeout(function () {
            wx.navigateBack(); //返回到登录页
          }, 1500);
        } else {
          App.WxService.showToast({
            title: "注册失败，该用户名已存在",
            icon: "none",
            duration: 1500,
          });
        }
      });
    }
  },
  resetForm: function () {
    console.log("form发生了reset事件");
  },
  //选择性别
  changeRadio1(evt) {
    if (evt.detail) {
      evt = evt.detail.value;
    }
    let value = evt;
    let radioDatas1 = this.data.radioDatas1;
    let radioLabel1 = this.data.radioLabel1;
    for (var i = 0, len = radioDatas1.length; i < len; ++i) {
      radioDatas1[i].checked = radioDatas1[i].value == value;
      if (radioDatas1[i].checked) {
        radioLabel1 = radioDatas1[i].label;
      }
    }
    this.setData({ radioLabel1, radioDatas1 });
  },
  onChangeAddress: function () {
    var _page = this;
    wx.chooseLocation({
      success: function (res) {
        console.log(res);
        _page.setData({
          address: res.address,
        });
      },
      fail: function (err) {
        console.log(err);
      },
    });
  },

  changepic: function (values) {
    if (values.detail) {
      values = values.detail.value;
    } else if (App.Tools.isString(values)) {
      values = values.split(",");
    }
    var files = [];
    for (var i = 0; i < values.length; i++) {
      files[i] = App.renderImage(values[i]);
    }
    this.setData({
      files2: files,
      filestr2: (files || []).join(",").replace(/^[]/, ""),
    });
  },
  chooseImage2: function (e) {
    var that = this;
    wx.chooseImage({
      sizeType: ["original", "compressed"], // 可以指定是原图还是压缩图，默认二者都有
      sourceType: ["album", "camera"], // 可以指定来源是相册还是相机，默认二者都有
      success: function (res) {
        // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
        var tempFilePaths = res.tempFilePaths;
        for (var i = 0; i < tempFilePaths.length; i++) {
          wx.uploadFile({
            url: App.getActionUrl("/common/uploadFile.action"), //仅为示例，非真实的接口地址
            filePath: tempFilePaths[0],
            name: "file",
            success: function (res) {
              try {
                // 解析后端返回的JSON数据
                var responseData = JSON.parse(res.data);
                if (responseData.code === 200 && responseData.resdata && responseData.resdata.filePath) {
                  that.setData({
                    filestr2: responseData.resdata.filePath,
                  });
                } else {
                  App.showToast("图片上传失败", "none");
                }
              } catch (error) {
                console.error("解析上传响应失败:", error);
                App.showToast("图片上传失败", "none");
              }
            },
            fail: function (error) {
              console.error("图片上传失败:", error);
              App.showToast("图片上传失败", "none");
            }
          });
        }
      },
    });
  },
});
