﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
      <div class="echart" id="mychart" :style="myChartStyle"></div>
 
    </div>
</template>

<script>

 import * as echarts from "echarts";
 import request, { base } from "../../../../utils/http";
 
 export default {
   data() {
     return {
       myChartStyle: {
         height: "500px",
         width: "100%"
       },
       pieData: [],
       pieName: [],
     };
   },
   mounted() {
     this.getdata();
     this.initEcharts();
   },
   methods: {
 
     //数据初始化
     getdata() {
       let url = base + "/ReportData/queryReport4";
 
       let para = {};
 
       request.post(url, para).then((res) => {
         if (res.code == 200) {
           var ss = res.resdata;
           var pieName2 = [];
           var pieData2 = [];
           for (let i = 0; i < ss.length; i++) {
             pieName2[i] = ss[i].name;
             pieData2[i] = ss[i].num;
           }
           this.pieName = pieName2;
           this.pieData = pieData2;
           this.initEcharts();
         } else {
           this.$message.error(res.msg);
         }
       });
     },
 
     initEcharts() {
       const chartDom = document.getElementById("mychart");
       const myChart = echarts.init(chartDom);
       const option = {
         xAxis: {
           type: "category",
           data: this.pieName
         },
         tooltip:
         {
           trigger: 'axis',
 
           axisPointer: {
 
             type: 'cross',
             label: {
               backgroundColor: '#6a7985'
             }
           }
         },
         yAxis: {
           type: "value"
         },
         series: [
           {
             data: this.pieData,
             type: "bar",
             smooth: true
           }
         ]
       };
       myChart.setOption(option);
     }
   }
 };
</script>
<style scoped>
</style>
 

