package com.controller;

import com.model.ReportData;
import com.response.Response;
import com.service.ReportDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
public class ReportDataAction {

	@Autowired
	private ReportDataService reportDataService;

	// 商品分类销量统计
	@RequestMapping(value = "/queryReport")
	@CrossOrigin
	public Response<List<ReportData>> queryReport(@RequestBody ReportData reportdata, HttpServletRequest req)
			throws Exception {
		reportdata.setSql("SELECT c.typename as name, COALESCE(SUM(o.qty), 0) as num " +
				"FROM categorys c " +
				"LEFT JOIN goods g ON c.typeid = g.typeid " +
				"LEFT JOIN orders o ON g.gid = o.gid " + reportdata.getBy1() +
				"GROUP BY c.typename " +
				"ORDER BY num DESC");
		List<ReportData> data = reportDataService.report(reportdata);
		return Response.success(data);
	}

}

