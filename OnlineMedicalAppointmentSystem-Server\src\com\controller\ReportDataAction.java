package com.controller;

import com.model.ReportData;
import com.response.Response;
import com.service.ReportDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
public class ReportDataAction {

	@Autowired
	private ReportDataService reportDataService;

	// 科室预约统计（饼图）
	@RequestMapping(value = "/queryReport")
	@CrossOrigin
	@ResponseBody
	public Response<List<ReportData>> queryReport(@RequestBody ReportData reportdata, HttpServletRequest req)
			throws Exception {
		reportdata.setSql("SELECT p.pname as name, COUNT(r.rid) as num " +
				"FROM parts p " +
				"LEFT JOIN doctor d ON p.pid = d.pid " +
				"LEFT JOIN reserve r ON d.did = r.did " +
				"GROUP BY p.pid, p.pname " +
				"ORDER BY num DESC");
		List<ReportData> data = reportDataService.report(reportdata);
		return Response.success(data);
	}

	// 医生预约统计（柱状图）
	@RequestMapping(value = "/queryReport2")
	@CrossOrigin
	@ResponseBody
	public Response<List<ReportData>> queryReport2(@RequestBody ReportData reportdata, HttpServletRequest req)
			throws Exception {
		reportdata.setSql("SELECT d.dname as name, COUNT(r.rid) as num " +
				"FROM doctor d " +
				"LEFT JOIN reserve r ON d.did = r.did " +
				"GROUP BY d.did, d.dname " +
				"ORDER BY num DESC " +
				"LIMIT 10");
		List<ReportData> data = reportDataService.report(reportdata);
		return Response.success(data);
	}

	// 日预约统计（折线图）
	@RequestMapping(value = "/queryReport3")
	@CrossOrigin
	@ResponseBody
	public Response<List<ReportData>> queryReport3(@RequestBody ReportData reportdata, HttpServletRequest req)
			throws Exception {
		String doctorCondition = "";
		if (reportdata.getBy1() != null && !reportdata.getBy1().isEmpty()) {
			doctorCondition = " AND r.did = " + reportdata.getBy1();
		}

		reportdata.setSql("SELECT DATE(r.addtime) as name, COUNT(r.rid) as num " +
				"FROM reserve r " +
				"WHERE r.addtime >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)" + doctorCondition + " " +
				"GROUP BY DATE(r.addtime) " +
				"ORDER BY DATE(r.addtime)");
		List<ReportData> data = reportDataService.report(reportdata);
		return Response.success(data);
	}

	// 月预约统计（柱状图）
	@RequestMapping(value = "/queryReport4")
	@CrossOrigin
	@ResponseBody
	public Response<List<ReportData>> queryReport4(@RequestBody ReportData reportdata, HttpServletRequest req)
			throws Exception {
		String doctorCondition = "";
		if (reportdata.getBy1() != null && !reportdata.getBy1().isEmpty()) {
			doctorCondition = " AND r.did = " + reportdata.getBy1();
		}

		reportdata.setSql("SELECT DATE_FORMAT(r.addtime, '%Y-%m') as name, COUNT(r.rid) as num " +
				"FROM reserve r " +
				"WHERE r.addtime >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)" + doctorCondition + " " +
				"GROUP BY DATE_FORMAT(r.addtime, '%Y-%m') " +
				"ORDER BY DATE_FORMAT(r.addtime, '%Y-%m')");
		List<ReportData> data = reportDataService.report(reportdata);
		return Response.success(data);
	}

}

