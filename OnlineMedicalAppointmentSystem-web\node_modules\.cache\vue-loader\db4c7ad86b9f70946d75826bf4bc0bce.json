{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total1.vue?vue&type=template&id=75ec1912", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total1.vue", "mtime": 1749221396996}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgICA8ZGl2IHN0eWxlPSJ3aWR0aDogMTAwJTtsaW5lLWhlaWdodDogMzBweDt0ZXh0LWFsaWduOiBsZWZ0OyI+CiAgICAgICAgPCEtLSDmk43kvZzmjInpkq4gLS0+CiAgICAgICAgPGRpdiBzdHlsZT0ibWFyZ2luLWJvdHRvbTogMjBweDsgdGV4dC1hbGlnbjogcmlnaHQ7Ij4KICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImV4cG9ydFRvRXhjZWwiIDpsb2FkaW5nPSJleHBvcnRMb2FkaW5nIj4KICAgICAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLWRvd25sb2FkIj48L2k+IOWvvOWHukV4Y2VsCiAgICAgICAgICAgIDwvZWwtYnV0dG9uPgogICAgICAgIDwvZGl2PgoKICAgICAgICA8IS0tIOWbvuihqOWuueWZqCAtLT4KICAgICAgICA8ZGl2IGNsYXNzPSJlY2hhcnQiIGlkPSJteWNoYXJ0IiA6c3R5bGU9Im15Q2hhcnRTdHlsZSI+PC9kaXY+CiAgICA8L2Rpdj4K"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total1.vue"], "names": [], "mappings": ";IACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/admin/total/Total1.vue", "sourceRoot": "", "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n        <!-- 操作按钮 -->\n        <div style=\"margin-bottom: 20px; text-align: right;\">\n            <el-button type=\"primary\" @click=\"exportToExcel\" :loading=\"exportLoading\">\n                <i class=\"el-icon-download\"></i> 导出Excel\n            </el-button>\n        </div>\n\n        <!-- 图表容器 -->\n        <div class=\"echart\" id=\"mychart\" :style=\"myChartStyle\"></div>\n    </div>\n</template>\n\n<script>\n\n import request, { base } from \"../../../../utils/http\";\n import * as echarts from \"echarts\";\n \n export default {\n   data() {\n     return {\n       myChart: {},\n       pieData: [\n       ],\n       pieName: [],\n       myChartStyle: { float: \"left\", width: \"100%\", height: \"550px\" } //图表样式\n     };\n   },\n   mounted() {\n     this.getdata();\n   },\n   methods: {\n \n     //数据初始化\n     getdata() {\n       let url = base + \"/ReportData/queryReport\";\n \n       let para = {\n       };\n \n       request.post(url, para).then((res) => {\n         if (res.code == 200) {\n           var ss = res.resdata;\n \n           for (let i = 0; i < ss.length; i++) {\n             this.pieData[i] = {\n               name: ss[i].name,\n               value: ss[i].num\n             };\n \n             this.initDate();\n             this.initEcharts();\n           }\n         } else {\n           this.$message.error(res.msg);\n         }\n       });\n \n     },\n \n \n     initDate() {\n       for (let i = 0; i < this.pieData.length; i++) {\n         this.pieName[i] = this.pieData[i].name;\n       }\n     },\n     initEcharts() {\n       // 饼图\n       const option = {\n         legend: {\n           // 图例\n           data: this.pieName,\n           right: \"10%\",\n           top: \"10%\",\n           orient: \"vertical\"\n         },\n         title: {\n \n           text: \"产品分类销售统计\",\n           top: \"10%\",\n           left: \"center\"\n         },\n         series: [\n           {\n             type: \"pie\",\n             label: {\n               show: true,\n               formatter: \"{b} : {c} ({d}%)\" // b代表名称，c代表对应值，d代表百分比\n             },\n             radius: \"30%\", //饼图半径\n             data: this.pieData\n           }\n         ]\n       };\n       console.log(this.seriesData);\n       const optionFree = {\n         xAxis: {},\n         yAxis: {},\n         series: [\n           {\n             data: this.seriesData,\n             type: \"line\",\n             smooth: true\n           }\n         ]\n       };\n       this.myChart = echarts.init(document.getElementById(\"mychart\"));\n       this.myChart.setOption(option);\n       //随着屏幕大小调节图表\n       window.addEventListener(\"resize\", () => {\n         this.myChart.resize();\n       });\n     }\n   }\n };\n</script>\n<style scoped>\n</style>\n \n\n"]}]}