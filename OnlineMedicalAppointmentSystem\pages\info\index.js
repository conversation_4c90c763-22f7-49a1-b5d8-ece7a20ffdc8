const App = getApp();
Page({
  data: {
    url: App.Config.fileBasePath,
    files2: [],
    filestr2: "",

    radioDatas1: [
      { value: "男", label: "男", checked: "true" },
      { value: "女", label: "女" },
    ],
    radioLabel1: "男",

    users: [], //存放用户信息
  },

  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
    this.init();
  },

  //页面显示
  onShow() {},

  async init() {
    this.getForm1();
    this.chushi();
  },

  //设置输入验证
  getForm1() {
    this.WxValidate = App.WxValidate({
      uname: {
        required: {
          message: "姓名：不能为空哟",
        },
      },
      uphone: {
        tel: {
          message: "手机号码：请输入11位的手机号码",
        },
        required: {
          message: "手机号码：不能为空哟",
        },
      },
      address: {
        required: {
          message: "家庭地址：不能为空哟",
        },
      },
    });
  },

  //修改
  submitForm: function (e) {
    if (!this.WxValidate.checkForm(e)) {
      //输入验证不通过时，弹出相应的提示信息，并返回
      const error = this.WxValidate.errorList[0];
      var msg = `${error.msg}`;
      App.showToast(msg, "none");
      return false;
    } else {
      var data = e.detail.value;

      data = App.Tools.extend(data, {
        f: 1,
        pic: this.data.filestr2,
        lname: wx.getStorageSync("lname"),
      });

      App.HttpService.saveData(data, "/users/update2.action").then((data) => {
        //调用服务器接口，并根据返回值，弹出相应的提示
        App.WxService.showToast({
          title: "修改成功!",
          icon: "success",
          duration: 1500,
        });
        setTimeout(function () {
          wx.navigateBack(); //返回上页
        }, 1500);
      });
    }
  },
  resetForm: function () {
    console.log("form发生了reset事件");
  },
  //选择性别
  changeRadio1(evt) {
    if (evt.detail) {
      evt = evt.detail.value;
    }
    let value = evt;
    let radioDatas1 = this.data.radioDatas1;
    let radioLabel1 = this.data.radioLabel1;
    for (var i = 0, len = radioDatas1.length; i < len; ++i) {
      radioDatas1[i].checked = radioDatas1[i].value == value;
      if (radioDatas1[i].checked) {
        radioLabel1 = radioDatas1[i].label;
      }
    }
    this.setData({ radioLabel1, radioDatas1 });
  },
  onChangeAddress: function () {
    var _page = this;
    wx.chooseLocation({
      success: function (res) {
        console.log(res);
        _page.setData({
          address: res.address,
        });
      },
      fail: function (err) {
        console.log(err);
      },
    });
  },

  changepic: function (values) {
    if (values.detail) {
      values = values.detail.value;
    } else if (App.Tools.isString(values)) {
      values = values.split(",");
    }
    var files = [];
    for (var i = 0; i < values.length; i++) {
      files[i] = App.renderImage(values[i]);
    }
    this.setData({
      files2: files,
      filestr2: (files || []).join(",").replace(/^[]/, ""),
    });
  },
  chooseImage2: function (e) {
    var that = this;
    wx.chooseImage({
      sizeType: ["original", "compressed"], // 可以指定是原图还是压缩图，默认二者都有
      sourceType: ["album", "camera"], // 可以指定来源是相册还是相机，默认二者都有
      success: function (res) {
        // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
        var tempFilePaths = res.tempFilePaths;
        for (var i = 0; i < tempFilePaths.length; i++) {
          wx.uploadFile({
            url: App.getActionUrl("/common/uploadFile.action"), //仅为示例，非真实的接口地址
            filePath: tempFilePaths[0],
            name: "file",
            success: function (res) {
              try {
                // 解析后端返回的JSON数据
                var responseData = JSON.parse(res.data);
                if (responseData.code === 200 && responseData.resdata && responseData.resdata.filePath) {
                  that.setData({
                    filestr2: responseData.resdata.filePath,
                  });
                } else {
                  App.showToast("图片上传失败", "none");
                }
              } catch (error) {
                console.error("解析上传响应失败:", error);
                App.showToast("图片上传失败", "none");
              }
            },
            fail: function (error) {
              console.error("图片上传失败:", error);
              App.showToast("图片上传失败", "none");
            }
          });
        }
      },
    });
  },

  //初始化用户信息
  chushi() {
    var that = this;
    //设置要传递的参数
    let param = {
      f: 1,
      lname: wx.getStorageSync("lname"),
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/users/list2.action?currentPage=1&pageSize=1").then((data) => {
      //调用服务器接口
      this.setData({
        filestr2: data.resdata[0].pic,

        users: data.resdata, //把从服务器端得到的值赋值给数组
      });
      that.changeRadio1(data.resdata[0].usex);
    });
  },
});
