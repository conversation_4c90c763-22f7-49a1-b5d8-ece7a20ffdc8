const App = getApp();
Page({
  data: {
    msgs1: [],
    msgs2: [],
    url: App.Config.fileBasePath,
  },

  //弹出提示信息
  showModal(message) {
    App.WxService.showModal({
      title: "友情提示",
      content: message,
      showCancel: !1,
    });
  },
  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
    this.getMsgs1();
  },

  //页面显示
  onShow() {
    this.init();
  },

  async init() {},

  getMsgs1() {
    var that = this;
    //设置要传递的参数
    let param = {
      f: 2,
      rid: this.data.globalOption.id,
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/reserve/list2.action?currentPage=1&pageSize=1").then((data) => {
      //执行服务器Servlet
      this.setData({
        msgs1: data.resdata, //把从服务器端得到的值赋值给数组
      });

      let param2 = {
        peoid: this.data.msgs1[0].peoid,
      };

      App.HttpService.getData(param2, "/patient/list.action?currentPage=1&pageSize=1").then((data) => {
        this.setData({
          msgs2: data.resdata,
        });
      });
    });
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },
});
