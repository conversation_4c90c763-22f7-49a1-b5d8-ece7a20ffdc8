const App = getApp();
Page({
  data: {
    users: [],
    uname: "",
    msg: "",
    url: App.Config.fileBasePath,
  },

  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
  },

  //页面显示
  onShow() {
    this.init();
  },

  async init() {
    this.chushi();
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },

  //初始化用户信息
  chushi() {
    var that = this;
    //设置要传递的参数
    let param = {
      f: 1,
      lname: wx.getStorageSync("lname"),
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/users/list2.action?currentPage=1&pageSize=1").then((data) => {
      //执行服务器Servlet
      this.setData({
        users: data.resdata, //把从服务器端得到的值赋值给数组
        msg: data.msg,
      });
    });
  },

  //退出登录
  tuichu() {
    wx.showModal({
      title: "提示",
      content: "确定要退出吗？",
      success(res) {
        if (res.confirm) {
          wx.clearStorageSync();
          wx.reLaunch({
            url: "/pages/login/index",
          });
        }
      },
    });
  },
});
