-- 医院挂号系统统计报表测试SQL

-- 1. 科室预约统计（饼图数据）
SELECT p.pname as name, COUNT(r.rid) as num 
FROM parts p 
LEFT JOIN doctor d ON p.pid = d.pid 
LEFT JOIN reserve r ON d.did = r.did 
GROUP BY p.pid, p.pname 
ORDER BY num DESC;

-- 2. 医生预约统计（柱状图数据，TOP 10）
SELECT d.dname as name, COUNT(r.rid) as num 
FROM doctor d 
LEFT JOIN reserve r ON d.did = r.did 
GROUP BY d.did, d.dname 
ORDER BY num DESC 
LIMIT 10;

-- 3. 日预约统计（折线图数据，最近30天）
SELECT DATE(r.addtime) as name, COUNT(r.rid) as num 
FROM reserve r 
WHERE r.addtime >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(r.addtime) 
ORDER BY DATE(r.addtime);

-- 4. 月预约统计（柱状图数据，最近12个月）
SELECT DATE_FORMAT(r.addtime, '%Y-%m') as name, COUNT(r.rid) as num 
FROM reserve r 
WHERE r.addtime >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
GROUP BY DATE_FORMAT(r.addtime, '%Y-%m') 
ORDER BY DATE_FORMAT(r.addtime, '%Y-%m');

-- 5. 指定医生的日预约统计（医生ID=1的示例）
SELECT DATE(r.addtime) as name, COUNT(r.rid) as num 
FROM reserve r 
WHERE r.addtime >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
AND r.did = 1
GROUP BY DATE(r.addtime) 
ORDER BY DATE(r.addtime);

-- 6. 指定医生的月预约统计（医生ID=1的示例）
SELECT DATE_FORMAT(r.addtime, '%Y-%m') as name, COUNT(r.rid) as num 
FROM reserve r 
WHERE r.addtime >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
AND r.did = 1
GROUP BY DATE_FORMAT(r.addtime, '%Y-%m') 
ORDER BY DATE_FORMAT(r.addtime, '%Y-%m');

-- 验证数据完整性的查询
-- 检查科室表
SELECT COUNT(*) as parts_count FROM parts;
SELECT * FROM parts LIMIT 5;

-- 检查医生表
SELECT COUNT(*) as doctor_count FROM doctor;
SELECT did, dname, pid FROM doctor LIMIT 5;

-- 检查预约表
SELECT COUNT(*) as reserve_count FROM reserve;
SELECT rid, did, addtime FROM reserve ORDER BY addtime DESC LIMIT 10;

-- 检查数据关联性
SELECT 
    p.pname as 科室名称,
    d.dname as 医生姓名,
    COUNT(r.rid) as 预约数量
FROM parts p 
LEFT JOIN doctor d ON p.pid = d.pid 
LEFT JOIN reserve r ON d.did = r.did 
GROUP BY p.pid, p.pname, d.did, d.dname
HAVING 预约数量 > 0
ORDER BY 预约数量 DESC;

-- 插入测试数据（如果需要）
-- 注意：运行前请确认是否需要插入测试数据

/*
-- 插入测试科室数据
INSERT INTO parts (pname) VALUES 
('内科'), ('外科'), ('儿科'), ('妇科'), ('眼科');

-- 插入测试医生数据
INSERT INTO doctor (daccount, password, dname, sex, jobs, tel, pid, addtime) VALUES 
('doc001', '123456', '张医生', '男', '主任医师', '***********', 1, NOW()),
('doc002', '123456', '李医生', '女', '副主任医师', '***********', 1, NOW()),
('doc003', '123456', '王医生', '男', '主治医师', '***********', 2, NOW()),
('doc004', '123456', '赵医生', '女', '主治医师', '***********', 3, NOW()),
('doc005', '123456', '刘医生', '男', '副主任医师', '***********', 4, NOW());

-- 插入测试预约数据（最近30天的随机数据）
INSERT INTO reserve (pid, did, rdate, rtime, lname, peoid, addtime, flag) VALUES 
(1, 1, CURDATE(), '8:00-9:00', 'user001', 1, DATE_SUB(NOW(), INTERVAL 1 DAY), '已预约'),
(1, 1, CURDATE(), '9:00-10:00', 'user002', 2, DATE_SUB(NOW(), INTERVAL 2 DAY), '已预约'),
(1, 2, CURDATE(), '10:00-11:00', 'user003', 3, DATE_SUB(NOW(), INTERVAL 3 DAY), '已预约'),
(2, 3, CURDATE(), '14:00-15:00', 'user004', 4, DATE_SUB(NOW(), INTERVAL 5 DAY), '已预约'),
(3, 4, CURDATE(), '15:00-16:00', 'user005', 5, DATE_SUB(NOW(), INTERVAL 7 DAY), '已预约'),
(4, 5, CURDATE(), '16:00-17:00', 'user006', 6, DATE_SUB(NOW(), INTERVAL 10 DAY), '已预约');
*/
