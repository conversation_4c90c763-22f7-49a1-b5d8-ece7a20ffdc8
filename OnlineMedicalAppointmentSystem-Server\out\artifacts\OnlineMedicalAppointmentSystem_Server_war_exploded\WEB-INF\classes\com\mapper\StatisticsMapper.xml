<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.StatisticsMapper">

    <!-- 获取患者总数 -->
    <select id="getPatientCount" resultType="int">
        SELECT COUNT(*) FROM users
    </select>
    
    <!-- 获取医生总数 -->
    <select id="getDoctorCount" resultType="int">
        SELECT COUNT(*) FROM doctor
    </select>
    
    <!-- 获取今日挂号总数 -->
    <select id="getTodayAppointmentCount" resultType="int">
        SELECT COUNT(*) FROM reserve 
        WHERE DATE(addtime) = CURDATE()
    </select>
    
    <!-- 获取总挂号数 -->
    <select id="getTotalAppointmentCount" resultType="int">
        SELECT COUNT(*) FROM reserve
    </select>
    
    <!-- 获取指定医生今日挂号数 -->
    <select id="getDoctorTodayAppointmentCount" parameterType="int" resultType="int">
        SELECT COUNT(*) FROM reserve 
        WHERE did = #{doctorId} AND DATE(addtime) = CURDATE()
    </select>
    
    <!-- 获取指定医生总挂号数 -->
    <select id="getDoctorTotalAppointmentCount" parameterType="int" resultType="int">
        SELECT COUNT(*) FROM reserve 
        WHERE did = #{doctorId}
    </select>

</mapper>
