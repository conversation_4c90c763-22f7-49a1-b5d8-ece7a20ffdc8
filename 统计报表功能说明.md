# 医院挂号系统统计报表功能实现说明

## 功能概述

本次实现了4个统计报表功能，支持图表展示和Excel导出：

### 管理员可查看的统计报表：
1. **科室预约统计（饼图）** - `/total1`
2. **医生预约统计（柱状图）** - `/total2`
3. **日预约统计（折线图）** - `/total3`
4. **月预约统计（柱状图）** - `/total4`

### 医生可查看的统计报表：
1. **我的日预约统计（折线图）** - `/total3`
2. **我的月预约统计（柱状图）** - `/total4`

## 技术实现

### 后端实现
- **控制器**：`ReportDataAction.java` - 新增4个统计接口
- **数据查询**：基于现有的 `ReportData` 模型和服务
- **权限控制**：医生只能查看自己的数据（通过 `by1` 参数传递医生ID）

### 前端实现
- **图表库**：使用 ECharts 5.2.2
- **Excel导出**：使用 xlsx 和 file-saver 库
- **权限控制**：根据用户角色显示不同的菜单和数据

## 安装和部署

### 1. 安装前端依赖
```bash
cd OnlineMedicalAppointmentSystem-web
npm install
```

新增的依赖包：
- `xlsx@^0.18.5` - Excel文件处理
- `file-saver@^2.0.5` - 文件下载

### 2. 后端部署
后端代码已更新，重新编译部署即可。

### 3. 数据库要求
确保以下表存在且有数据：
- `parts` - 科室表
- `doctor` - 医生表  
- `reserve` - 预约挂号表

## API接口说明

### 1. 科室预约统计
- **接口**：`POST /ReportData/queryReport`
- **SQL**：统计各科室的预约数量
- **图表类型**：饼图

### 2. 医生预约统计
- **接口**：`POST /ReportData/queryReport2`
- **SQL**：统计各医生的预约数量（TOP 10）
- **图表类型**：柱状图

### 3. 日预约统计
- **接口**：`POST /ReportData/queryReport3`
- **参数**：`by1` - 医生ID（医生角色时传递）
- **SQL**：统计最近30天的每日预约数量
- **图表类型**：折线图

### 4. 月预约统计
- **接口**：`POST /ReportData/queryReport4`
- **参数**：`by1` - 医生ID（医生角色时传递）
- **SQL**：统计最近12个月的每月预约数量
- **图表类型**：柱状图

## Excel导出功能

每个统计页面都包含"导出Excel"按钮，支持：
- 自动生成文件名（包含日期）
- 设置合适的列宽
- 包含序号、名称、数量等字段
- 支持中文文件名

## 权限控制

### 管理员权限
- 可查看所有4个统计报表
- 查看全系统的统计数据

### 医生权限
- 只能查看日预约统计和月预约统计
- 只能查看自己的预约数据
- 菜单中不显示科室统计和医生统计

## 使用说明

1. **登录系统**：使用管理员或医生账号登录
2. **访问统计**：在左侧菜单中选择"统计报表"
3. **查看图表**：系统自动加载并显示图表
4. **导出Excel**：点击"导出Excel"按钮下载数据

## 注意事项

1. 确保数据库中有足够的测试数据
2. 医生登录时需要正确的医生ID存储在session中
3. 图表会根据屏幕大小自动调整
4. Excel导出功能在现代浏览器中工作最佳

## 故障排除

### 常见问题
1. **图表不显示**：检查后端接口是否正常，数据库连接是否正常
2. **Excel导出失败**：检查浏览器是否支持文件下载
3. **医生看不到数据**：检查医生ID是否正确传递
4. **权限错误**：检查用户角色和session存储

### 调试方法
1. 打开浏览器开发者工具查看网络请求
2. 检查控制台是否有JavaScript错误
3. 验证后端接口返回的数据格式
4. 确认数据库查询结果是否正确
