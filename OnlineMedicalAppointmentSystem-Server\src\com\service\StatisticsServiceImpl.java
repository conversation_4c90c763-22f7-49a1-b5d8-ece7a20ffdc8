package com.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mapper.StatisticsMapper;

/**
 * 统计服务实现类
 */
@Service
public class StatisticsServiceImpl implements StatisticsService {
    
    @Autowired
    private StatisticsMapper statisticsMapper;
    
    @Override
    public int getPatientCount() {
        return statisticsMapper.getPatientCount();
    }
    
    @Override
    public int getDoctorCount() {
        return statisticsMapper.getDoctorCount();
    }
    
    @Override
    public int getTodayAppointmentCount() {
        return statisticsMapper.getTodayAppointmentCount();
    }
    
    @Override
    public int getTotalAppointmentCount() {
        return statisticsMapper.getTotalAppointmentCount();
    }
    
    @Override
    public int getDoctorTodayAppointmentCount(Integer doctorId) {
        return statisticsMapper.getDoctorTodayAppointmentCount(doctorId);
    }
    
    @Override
    public int getDoctorTotalAppointmentCount(Integer doctorId) {
        return statisticsMapper.getDoctorTotalAppointmentCount(doctorId);
    }
}
