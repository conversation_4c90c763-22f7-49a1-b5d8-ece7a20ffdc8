package com.controller;

import com.model.Reserve;
import com.response.Response;
import com.service.ReserveService;
import com.util.PageBean;
import com.util.removeHTML;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/api/reserve")
public class ReserveController{
	
	@Resource
	private ReserveService reserveService;
	
	//预约挂号列表
	@RequestMapping(value="/list")
	@CrossOrigin
	public Response<List<Reserve>> list(@RequestBody Reserve reserve, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = reserveService.getCount(reserve);
		//获取当前页记录
		List<Reserve> reserveList = reserveService.queryReserveList(reserve, page);
		//遍历
		for (Reserve reserve2 : reserveList) {
			reserve2.setResults(removeHTML.Html2Text(reserve2.getResults()));

		}

		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(reserveList, counts, page_count);
	}

		//预约挂号列表
	@RequestMapping(value="/list2")
	@CrossOrigin
	public Response<List<Reserve>> list2( Reserve reserve, @RequestParam Integer currentPage, HttpServletRequest req) throws Exception {
		int pageSize=Integer.parseInt(req.getParameter("pageSize")); //每页显示记录数
		int offset = (currentPage - 1) * pageSize; //当前页开始记录
		int counts = 0;  //总记录数
		PageBean page = new PageBean(offset, pageSize); //分页对象
		//查询记录总数
		counts = reserveService.getCount(reserve);
		//获取当前页记录
		List<Reserve> reserveList = reserveService.queryReserveList(reserve, page);
		//遍历
		for (Reserve reserve2 : reserveList) {
			reserve2.setResults(removeHTML.Html2Text(reserve2.getResults()));

		}

		int page_count = counts % PageBean.PAGE_IETM == 0 ? counts / PageBean.PAGE_IETM : counts / PageBean.PAGE_IETM + 1; //总页数
		return Response.success(reserveList, counts, page_count);
	}
        
	//添加预约挂号
	@ResponseBody
	@PostMapping(value = "/add")
	@CrossOrigin
	public Response add( Reserve reserve, HttpServletRequest req) throws Exception {
		try {

			reserve.setFlag(new String(reserve.getFlag().getBytes("ISO-8859-1"), "UTF-8"));
			reserveService.insertReserve(reserve); //添加
   
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
    
	//删除预约挂号
	@ResponseBody
	@PostMapping(value = "/del")
	@CrossOrigin
	public Response del(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			reserveService.deleteReserve(id); //删除
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//修改预约挂号
	@ResponseBody
	@PostMapping(value = "/update")
	@CrossOrigin
	public Response update(@RequestBody Reserve reserve, HttpServletRequest req) throws Exception {
		try {
			reserveService.updateReserve(reserve); //修改
			} catch (Exception e) {
			return Response.error();
		}
		return Response.success();
	}
	
	//返回预约挂号详情
	@ResponseBody
	@PostMapping(value = "/get")
	@CrossOrigin
	public Response get(HttpServletRequest req) throws Exception {
		try {
			int id = Integer.parseInt(req.getParameter("id"));
			Reserve reserve=reserveService.queryReserveById(id); //根据ID查询
			return Response.success(reserve);
			} catch (Exception e) {
			return Response.error();
		}
       
	}
    
}

