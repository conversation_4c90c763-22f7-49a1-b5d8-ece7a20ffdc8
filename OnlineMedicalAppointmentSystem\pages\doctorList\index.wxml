<view class="container">
    <diy-navbar bgColor="green" isBack="{{false}}">

        <view slot="content"> 挂号 </view>
    </diy-navbar>
    <view class="flex diy-col-24 flex-direction-column margin-tb-sm">
        <scroll-view scroll-x scroll-into-view="{{'scroll-' + tabsIndex}}" scroll-left="{{tabsLeft}}"
            scroll-with-animation class="diy-tabs text-center solid-bottom justify-center scale-title small-border">
            <view id="{{'scroll-' + index}}"
                class="diy-tab-item flex-sub {{index==tabsIndex?' tabs-title-cur cur text-green':''}}"
                wx:for="{{tabsDatas}}" wx:for-item="item" wx:for-index="index" data-key="index" data-index="{{index}}"
                catchtap="changeTabs">{{item.pname}} </view>
        </scroll-view>
    </view>
    <block wx:for="{{msgs1}}" wx:key="k">
        <view class="flex diy-col-24 items-start flex-nowrap flex8-clz" bindtap="navigateTo" data-url="doctorView"
            data-id="{{item.did}}">
            <image src="{{url}}{{item.photo}}" class="diy-image diy-col-0 image3-clz"
                style="height: 100px !important; width: 100px"></image>
            <view class="flex flex-wrap diy-col-0 flex-direction-column flex12-clz">
                <view class="diy-col-24 diy-text-md"> {{item.dname}}
                    <text class="cu-tag  padding-left-sm bg-green">{{item.jobs}}</text>
                </view>
                <view class="flex flex-wrap diy-col-24 justify-between items-center flex16-clz text-cut">
                    <view class="flex diy-col-0 flex-nowrap flex18-clz">
                        <view class="diy-col-0 text19-clz">{{item.sex}} </view>
                        <view class="diy-col-0 text19-clz">{{item.pname}} </view>

                    </view>
                    <view class="diy-col-0 text22-clz text-price" style="color:red;border:0px;font-size: 22px;">
                        {{item.price}} </view>
                </view>
             
            </view>
        </view>
    </block>


    <view class="clearfix"></view>
</view>