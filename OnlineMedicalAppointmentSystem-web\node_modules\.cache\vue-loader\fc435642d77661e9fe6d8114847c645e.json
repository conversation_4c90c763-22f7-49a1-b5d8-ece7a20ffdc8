{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\Home.vue?vue&type=style&index=0&id=a44c444e&scoped=true&lang=css", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\Home.vue", "mtime": 1749196546366}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749193686285}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1749193687363}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749193686702}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouc3RhdC1jYXJkIHsNCiAgd2lkdGg6IDIzMHB4Ow0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2U7DQp9DQoNCi5zdGF0LWNhcmQ6aG92ZXIgew0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTVweCk7DQp9DQoNCi5zdGF0LWNvbnRlbnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiAxMHB4Ow0KfQ0KDQouc3RhdC1pY29uIHsNCiAgd2lkdGg6IDYwcHg7DQogIGhlaWdodDogNjBweDsNCiAgYm9yZGVyLXJhZGl1czogNTAlOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICBmb250LXNpemU6IDI0cHg7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLnN0YXQtaWNvbi5wYXRpZW50cyB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQp9DQoNCi5zdGF0LWljb24uZG9jdG9ycyB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmMDkzZmIgMCUsICNmNTU3NmMgMTAwJSk7DQp9DQoNCi5zdGF0LWljb24udG9kYXktYXBwb2ludG1lbnRzIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzRmYWNmZSAwJSwgIzAwZjJmZSAxMDAlKTsNCn0NCg0KLnN0YXQtaWNvbi50b3RhbC1hcHBvaW50bWVudHMgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNDNlOTdiIDAlLCAjMzhmOWQ3IDEwMCUpOw0KfQ0KDQouc3RhdC1pbmZvIGgzIHsNCiAgZm9udC1zaXplOiAzMnB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCiAgbWFyZ2luOiAwOw0KICBjb2xvcjogIzMwMzEzMzsNCn0NCg0KLnN0YXQtaW5mbyBwIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzkwOTM5OTsNCiAgbWFyZ2luOiA1cHggMCAwIDA7DQp9DQoNCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICAuc3RhdC1jYXJkIHsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBtYXgtd2lkdGg6IDMwMHB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\Home.vue"], "names": [], "mappings": ";AA4KA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACd;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;AACnB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB;AACF", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/admin/Home.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div style=\"width: 100%; padding: 20px;\" id=\"home\">\r\n    <!-- 欢迎信息 -->\r\n    <div style=\"text-align: center; margin-bottom: 30px;\">\r\n      <h2 style=\"color: #409EFF; margin-bottom: 10px;\">欢迎使用医院挂号预约系统</h2>\r\n      <p style=\"font-size: 16px; color: #666;\">\r\n        账号：<b style=\"color: #E6A23C;\">{{ userLname }}</b>，\r\n        身份：<b style=\"color: #E6A23C;\">{{ role }}</b>\r\n      </p>\r\n    </div>\r\n\r\n    <!-- 统计卡片 -->\r\n    <div v-loading=\"loading\" style=\"display: flex; flex-wrap: wrap; gap: 20px; justify-content: center;\">\r\n      <!-- 管理员统计卡片 -->\r\n      <template v-if=\"role === '管理员'\">\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon patients\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.patientCount || 0 }}</h3>\r\n              <p>患者总数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon doctors\">\r\n              <i class=\"el-icon-user-solid\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.doctorCount || 0 }}</h3>\r\n              <p>医生总数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon today-appointments\">\r\n              <i class=\"el-icon-date\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.todayAppointmentCount || 0 }}</h3>\r\n              <p>今日挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon total-appointments\">\r\n              <i class=\"el-icon-document\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.totalAppointmentCount || 0 }}</h3>\r\n              <p>总挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </template>\r\n\r\n      <!-- 医生统计卡片 -->\r\n      <template v-if=\"role === '医生'\">\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon today-appointments\">\r\n              <i class=\"el-icon-date\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.doctorTodayAppointmentCount || 0 }}</h3>\r\n              <p>我的今日挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon total-appointments\">\r\n              <i class=\"el-icon-document\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.doctorTotalAppointmentCount || 0 }}</h3>\r\n              <p>我的总挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </template>\r\n    </div>\r\n\r\n    <!-- 操作提示 -->\r\n    <div style=\"text-align: center; margin-top: 40px; color: #909399;\">\r\n      <p style=\"font-size: 14px;\">请在左侧菜单中选择您要进行的操作！</p>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      loading: false,\r\n      statistics: {\r\n        patientCount: 0,\r\n        doctorCount: 0,\r\n        todayAppointmentCount: 0,\r\n        totalAppointmentCount: 0,\r\n        doctorTodayAppointmentCount: 0,\r\n        doctorTotalAppointmentCount: 0\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.loadStatistics();\r\n  },\r\n  methods: {\r\n    // 加载统计数据\r\n    async loadStatistics() {\r\n      this.loading = true;\r\n      try {\r\n        const user = JSON.parse(sessionStorage.getItem(\"user\"));\r\n        let url = base + \"/statistics/dashboard\";\r\n\r\n        const params = {\r\n          role: this.role\r\n        };\r\n\r\n        // 如果是医生，传递医生ID\r\n        if (this.role === '医生' && user) {\r\n          params.doctorId = user.did;\r\n        }\r\n\r\n        const response = await request.post(url, params);\r\n        if (response.code === 200) {\r\n          this.statistics = response.resdata;\r\n        } else {\r\n          this.$message.error('获取统计数据失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('加载统计数据失败:', error);\r\n        // 如果后端服务器未启动，显示模拟数据用于演示\r\n        if (this.role === '管理员') {\r\n          this.statistics = {\r\n            patientCount: 156,\r\n            doctorCount: 23,\r\n            todayAppointmentCount: 45,\r\n            totalAppointmentCount: 1234\r\n          };\r\n        } else if (this.role === '医生') {\r\n          this.statistics = {\r\n            doctorTodayAppointmentCount: 12,\r\n            doctorTotalAppointmentCount: 89\r\n          };\r\n        }\r\n        this.$message.warning('后端服务器未连接，显示模拟数据');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.stat-card {\r\n  width: 230px;\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.stat-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.stat-icon.patients {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.stat-icon.doctors {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.stat-icon.today-appointments {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.stat-icon.total-appointments {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.stat-info h3 {\r\n  font-size: 32px;\r\n  font-weight: bold;\r\n  margin: 0;\r\n  color: #303133;\r\n}\r\n\r\n.stat-info p {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin: 5px 0 0 0;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .stat-card {\r\n    width: 100%;\r\n    max-width: 300px;\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}