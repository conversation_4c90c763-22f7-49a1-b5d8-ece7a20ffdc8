const App = getApp();
Page({
  data: {
    msgs1: [],
    tabsDatas: [],
    tabsIndex: 0,
    url: App.Config.fileBasePath,
  },

  //弹出提示信息
  showModal(message) {
    App.WxService.showModal({
      title: "友情提示",
      content: message,
      showCancel: !1,
    });
  },
  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
  },

  //页面显示
  onShow() {
    this.init();
  },

  async init() {
    this.getTab1();

    this.getMsgs1();
  },

  getMsgs1() {
    var that = this;
    //设置要传递的参数
    let param = {
      f: 1,
      pid:
        this.data.tabsIndex == 0
          ? 0
          : this.data.tabsDatas[this.data.tabsIndex].pid,
      condition:
        " and (dname like '%" +
        App.globalData.sk +
        "%' or pname like '%" +
        App.globalData.sk +
        "%')",
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/doctor/list2.action?currentPage=1&pageSize=500").then((data) => {
      //执行服务器Servlet
      this.setData({
        msgs1: data.resdata, //把从服务器端得到的值赋值给数组
      });
    });

    App.globalData.sk = "";
  },

  getTab1() {
    //设置要传递的参数
    let param = {
      f: 3,
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/parts/list2.action?currentPage=1&pageSize=500").then((data) => {
      //加全部
      data.resdata.unshift({
        pid: 0,
        pname: "全部",
      });

      //加全部
      this.setData({
        tabsDatas: data.resdata,
      });
    });
  },

  changeTabs(evt) {
    let { index } = evt.currentTarget.dataset;
    if (index == this.data.tabsIndex) return;
    this.setData({
      tabsLeft: (index - 1) * 60,
      tabsIndex: index,
    });

    this.getMsgs1();
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },
});
