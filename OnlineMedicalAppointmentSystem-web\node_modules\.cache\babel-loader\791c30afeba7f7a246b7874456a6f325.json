{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total2.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total2.vue", "mtime": 1749221490972}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "echarts", "XLSX", "saveAs", "data", "pieData", "pieName", "exportLoading", "myChart", "mounted", "getdata", "window", "addEventListener", "resizeChart", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "url", "para", "post", "then", "res", "code", "ss", "resdata", "pieName2", "pieData2", "i", "length", "name", "num", "initEcharts", "$message", "error", "msg", "dom", "document", "getElementById", "init", "renderer", "option", "title", "tooltip", "trigger", "axisPointer", "type", "legend", "grid", "left", "right", "bottom", "containLabel", "xAxis", "boundaryGap", "yAxis", "series", "setOption", "getInstanceByDom", "resize"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total2.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n        <!-- 操作按钮 -->\n        <div style=\"margin-bottom: 20px; text-align: right;\">\n            <el-button type=\"primary\" @click=\"exportToExcel\" :loading=\"exportLoading\">\n                <i class=\"el-icon-download\"></i> 导出Excel\n            </el-button>\n        </div>\n\n        <!-- 图表容器 -->\n        <div id=\"container\" :style=\"{ height: '600px' }\"></div>\n    </div>\n</template>\n\n<script>\n\n import request, { base } from \"../../../../utils/http\";\n import * as echarts from 'echarts';\n import * as XLSX from 'xlsx';\n import { saveAs } from 'file-saver';\n\n export default {\n   data() {\n     return {\n       pieData: [],\n       pieName: [],\n       exportLoading: false,\n       myChart: null\n     };\n   },\n \n   mounted() {\n     this.getdata();\n     window.addEventListener('resize', this.resizeChart);\n   },\n   beforeDestroy() {\n     window.removeEventListener('resize', this.resizeChart);\n   },\n   methods: {\n     // 数据初始化\n     getdata() {\n       let url = base + \"/ReportData/queryReport2\";\n \n       let para = {};\n \n       request.post(url, para).then((res) => {\n         if (res.code == 200) {\n           var ss = res.resdata;\n           var pieName2 = [];\n           var pieData2 = [];\n           for (let i = 0; i < ss.length; i++) {\n             pieName2[i] = ss[i].name;\n             pieData2[i] = ss[i].num;\n           }\n           this.pieName = pieName2;\n           this.pieData = pieData2;\n           this.initEcharts();\n         } else {\n           this.$message.error(res.msg);\n         }\n       });\n     },\n \n     initEcharts() {\n       const dom = document.getElementById('container');\n       const myChart = echarts.init(dom, null, { renderer: 'canvas' });\n \n       const option = {\n         title: {\n \n         },\n         tooltip: {\n           trigger: 'axis',\n           axisPointer: {\n             type: 'shadow'\n           }\n         },\n         legend: {},\n         grid: {\n           left: '3%',\n           right: '4%',\n           bottom: '3%',\n           containLabel: true\n         },\n         xAxis: {\n           type: 'value',\n           boundaryGap: [0, 0.01]\n         },\n         yAxis: {\n           type: 'category',\n           data: this.pieName\n         },\n         series: [\n           {\n             name: '总销售数量',\n             type: 'bar',\n             data: this.pieData\n           }\n         ]\n       };\n \n       if (option && typeof option === 'object') {\n         myChart.setOption(option);\n       }\n     },\n     resizeChart() {\n       const dom = document.getElementById('container');\n       const myChart = echarts.getInstanceByDom(dom);\n       myChart.resize();\n     }\n   }\n };\n</script>\n<style scoped>\n</style>\n \n\n"], "mappings": "AAgBC,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,OAAO,KAAKC,OAAM,MAAO,SAAS;AAClC,OAAO,KAAKC,IAAG,MAAO,MAAM;AAC5B,SAASC,MAAK,QAAS,YAAY;AAEnC,eAAe;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXC,aAAa,EAAE,KAAK;MACpBC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,OAAO,CAAC,CAAC;IACdC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACC,WAAW,CAAC;EACrD,CAAC;EACDC,aAAaA,CAAA,EAAG;IACdH,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACF,WAAW,CAAC;EACxD,CAAC;EACDG,OAAO,EAAE;IACP;IACAN,OAAOA,CAAA,EAAG;MACR,IAAIO,GAAE,GAAIjB,IAAG,GAAI,0BAA0B;MAE3C,IAAIkB,IAAG,GAAI,CAAC,CAAC;MAEbnB,OAAO,CAACoB,IAAI,CAACF,GAAG,EAAEC,IAAI,CAAC,CAACE,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;UACnB,IAAIC,EAAC,GAAIF,GAAG,CAACG,OAAO;UACpB,IAAIC,QAAO,GAAI,EAAE;UACjB,IAAIC,QAAO,GAAI,EAAE;UACjB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIJ,EAAE,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;YAClCF,QAAQ,CAACE,CAAC,IAAIJ,EAAE,CAACI,CAAC,CAAC,CAACE,IAAI;YACxBH,QAAQ,CAACC,CAAC,IAAIJ,EAAE,CAACI,CAAC,CAAC,CAACG,GAAG;UACzB;UACA,IAAI,CAACxB,OAAM,GAAImB,QAAQ;UACvB,IAAI,CAACpB,OAAM,GAAIqB,QAAQ;UACvB,IAAI,CAACK,WAAW,CAAC,CAAC;QACpB,OAAO;UACL,IAAI,CAACC,QAAQ,CAACC,KAAK,CAACZ,GAAG,CAACa,GAAG,CAAC;QAC9B;MACF,CAAC,CAAC;IACJ,CAAC;IAEDH,WAAWA,CAAA,EAAG;MACZ,MAAMI,GAAE,GAAIC,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAC;MAChD,MAAM7B,OAAM,GAAIP,OAAO,CAACqC,IAAI,CAACH,GAAG,EAAE,IAAI,EAAE;QAAEI,QAAQ,EAAE;MAAS,CAAC,CAAC;MAE/D,MAAMC,MAAK,GAAI;QACbC,KAAK,EAAE,CAEP,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXC,IAAI,EAAE;UACR;QACF,CAAC;QACDC,MAAM,EAAE,CAAC,CAAC;QACVC,IAAI,EAAE;UACJC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,IAAI;UACZC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLP,IAAI,EAAE,OAAO;UACbQ,WAAW,EAAE,CAAC,CAAC,EAAE,IAAI;QACvB,CAAC;QACDC,KAAK,EAAE;UACLT,IAAI,EAAE,UAAU;UAChBzC,IAAI,EAAE,IAAI,CAACE;QACb,CAAC;QACDiD,MAAM,EAAE,CACN;UACE1B,IAAI,EAAE,OAAO;UACbgB,IAAI,EAAE,KAAK;UACXzC,IAAI,EAAE,IAAI,CAACC;QACb;MAEJ,CAAC;MAED,IAAImC,MAAK,IAAK,OAAOA,MAAK,KAAM,QAAQ,EAAE;QACxChC,OAAO,CAACgD,SAAS,CAAChB,MAAM,CAAC;MAC3B;IACF,CAAC;IACD3B,WAAWA,CAAA,EAAG;MACZ,MAAMsB,GAAE,GAAIC,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAC;MAChD,MAAM7B,OAAM,GAAIP,OAAO,CAACwD,gBAAgB,CAACtB,GAAG,CAAC;MAC7C3B,OAAO,CAACkD,MAAM,CAAC,CAAC;IAClB;EACF;AACF,CAAC", "ignoreList": []}]}