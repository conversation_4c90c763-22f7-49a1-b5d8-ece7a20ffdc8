{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total1.vue?vue&type=template&id=75ec1912", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total1.vue", "mtime": 1749221396996}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY3JlYXRlQ29tbWVudFZOb2RlIGFzIF9jcmVhdGVDb21tZW50Vk5vZGUsIGNyZWF0ZUVsZW1lbnRWTm9kZSBhcyBfY3JlYXRlRWxlbWVudFZOb2RlLCBjcmVhdGVUZXh0Vk5vZGUgYXMgX2NyZWF0ZVRleHRWTm9kZSwgcmVzb2x2ZUNvbXBvbmVudCBhcyBfcmVzb2x2ZUNvbXBvbmVudCwgd2l0aEN0eCBhcyBfd2l0aEN0eCwgY3JlYXRlVk5vZGUgYXMgX2NyZWF0ZVZOb2RlLCBub3JtYWxpemVTdHlsZSBhcyBfbm9ybWFsaXplU3R5bGUsIG9wZW5CbG9jayBhcyBfb3BlbkJsb2NrLCBjcmVhdGVFbGVtZW50QmxvY2sgYXMgX2NyZWF0ZUVsZW1lbnRCbG9jayB9IGZyb20gInZ1ZSI7CmNvbnN0IF9ob2lzdGVkXzEgPSB7CiAgc3R5bGU6IHsKICAgICJ3aWR0aCI6ICIxMDAlIiwKICAgICJsaW5lLWhlaWdodCI6ICIzMHB4IiwKICAgICJ0ZXh0LWFsaWduIjogImxlZnQiCiAgfQp9Owpjb25zdCBfaG9pc3RlZF8yID0gewogIHN0eWxlOiB7CiAgICAibWFyZ2luLWJvdHRvbSI6ICIyMHB4IiwKICAgICJ0ZXh0LWFsaWduIjogInJpZ2h0IgogIH0KfTsKZXhwb3J0IGZ1bmN0aW9uIHJlbmRlcihfY3R4LCBfY2FjaGUsICRwcm9wcywgJHNldHVwLCAkZGF0YSwgJG9wdGlvbnMpIHsKICBjb25zdCBfY29tcG9uZW50X2VsX2J1dHRvbiA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC1idXR0b24iKTsKICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCBfaG9pc3RlZF8xLCBbX2NyZWF0ZUNvbW1lbnRWTm9kZSgiIOaTjeS9nOaMiemSriAiKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfMiwgW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2J1dHRvbiwgewogICAgdHlwZTogInByaW1hcnkiLAogICAgb25DbGljazogX2N0eC5leHBvcnRUb0V4Y2VsLAogICAgbG9hZGluZzogX2N0eC5leHBvcnRMb2FkaW5nCiAgfSwgewogICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gX2NhY2hlWzBdIHx8IChfY2FjaGVbMF0gPSBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgiaSIsIHsKICAgICAgY2xhc3M6ICJlbC1pY29uLWRvd25sb2FkIgogICAgfSwgbnVsbCwgLTEgLyogSE9JU1RFRCAqLyksIF9jcmVhdGVUZXh0Vk5vZGUoIiDlr7zlh7pFeGNlbCAiKV0pKSwKICAgIF86IDEgLyogU1RBQkxFICovLAogICAgX186IFswXQogIH0sIDggLyogUFJPUFMgKi8sIFsib25DbGljayIsICJsb2FkaW5nIl0pXSksIF9jcmVhdGVDb21tZW50Vk5vZGUoIiDlm77ooajlrrnlmaggIiksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIHsKICAgIGNsYXNzOiAiZWNoYXJ0IiwKICAgIGlkOiAibXljaGFydCIsCiAgICBzdHlsZTogX25vcm1hbGl6ZVN0eWxlKCRkYXRhLm15Q2hhcnRTdHlsZSkKICB9LCBudWxsLCA0IC8qIFNUWUxFICovKV0pOwp9"}, {"version": 3, "names": ["style", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_button", "type", "onClick", "_ctx", "exportToExcel", "loading", "exportLoading", "_cache", "class", "id", "_normalizeStyle", "$data", "myChartStyle"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total1.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n        <!-- 操作按钮 -->\n        <div style=\"margin-bottom: 20px; text-align: right;\">\n            <el-button type=\"primary\" @click=\"exportToExcel\" :loading=\"exportLoading\">\n                <i class=\"el-icon-download\"></i> 导出Excel\n            </el-button>\n        </div>\n\n        <!-- 图表容器 -->\n        <div class=\"echart\" id=\"mychart\" :style=\"myChartStyle\"></div>\n    </div>\n</template>\n\n<script>\n\n import request, { base } from \"../../../../utils/http\";\n import * as echarts from \"echarts\";\n \n export default {\n   data() {\n     return {\n       myChart: {},\n       pieData: [\n       ],\n       pieName: [],\n       myChartStyle: { float: \"left\", width: \"100%\", height: \"550px\" } //图表样式\n     };\n   },\n   mounted() {\n     this.getdata();\n   },\n   methods: {\n \n     //数据初始化\n     getdata() {\n       let url = base + \"/ReportData/queryReport\";\n \n       let para = {\n       };\n \n       request.post(url, para).then((res) => {\n         if (res.code == 200) {\n           var ss = res.resdata;\n \n           for (let i = 0; i < ss.length; i++) {\n             this.pieData[i] = {\n               name: ss[i].name,\n               value: ss[i].num\n             };\n \n             this.initDate();\n             this.initEcharts();\n           }\n         } else {\n           this.$message.error(res.msg);\n         }\n       });\n \n     },\n \n \n     initDate() {\n       for (let i = 0; i < this.pieData.length; i++) {\n         this.pieName[i] = this.pieData[i].name;\n       }\n     },\n     initEcharts() {\n       // 饼图\n       const option = {\n         legend: {\n           // 图例\n           data: this.pieName,\n           right: \"10%\",\n           top: \"10%\",\n           orient: \"vertical\"\n         },\n         title: {\n \n           text: \"产品分类销售统计\",\n           top: \"10%\",\n           left: \"center\"\n         },\n         series: [\n           {\n             type: \"pie\",\n             label: {\n               show: true,\n               formatter: \"{b} : {c} ({d}%)\" // b代表名称，c代表对应值，d代表百分比\n             },\n             radius: \"30%\", //饼图半径\n             data: this.pieData\n           }\n         ]\n       };\n       console.log(this.seriesData);\n       const optionFree = {\n         xAxis: {},\n         yAxis: {},\n         series: [\n           {\n             data: this.seriesData,\n             type: \"line\",\n             smooth: true\n           }\n         ]\n       };\n       this.myChart = echarts.init(document.getElementById(\"mychart\"));\n       this.myChart.setOption(option);\n       //随着屏幕大小调节图表\n       window.addEventListener(\"resize\", () => {\n         this.myChart.resize();\n       });\n     }\n   }\n };\n</script>\n<style scoped>\n</style>\n \n\n"], "mappings": ";;EACSA,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;;EAEnDA,KAA+C,EAA/C;IAAA;IAAA;EAAA;AAA+C;;;uBAFxDC,mBAAA,CAUM,OAVNC,UAUM,GATFC,mBAAA,UAAa,EACbC,mBAAA,CAIM,OAJNC,UAIM,GAHFC,YAAA,CAEYC,oBAAA;IAFDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEC,IAAA,CAAAC,aAAa;IAAGC,OAAO,EAAEF,IAAA,CAAAG;;sBACvD,MAAgCC,MAAA,QAAAA,MAAA,OAAhCV,mBAAA,CAAgC;MAA7BW,KAAK,EAAC;IAAkB,4B,iBAAK,WACpC,E;;;+CAGJZ,mBAAA,UAAa,EACbC,mBAAA,CAA6D;IAAxDW,KAAK,EAAC,QAAQ;IAACC,EAAE,EAAC,SAAS;IAAEhB,KAAK,EAAAiB,eAAA,CAAEC,KAAA,CAAAC,YAAY", "ignoreList": []}]}