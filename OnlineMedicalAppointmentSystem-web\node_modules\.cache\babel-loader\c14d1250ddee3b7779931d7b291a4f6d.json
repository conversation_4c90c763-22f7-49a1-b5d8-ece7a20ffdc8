{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total1.vue?vue&type=template&id=75ec1912", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total1.vue", "mtime": 1749221024000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgbm9ybWFsaXplU3R5bGUgYXMgX25vcm1hbGl6ZVN0eWxlLCBjcmVhdGVFbGVtZW50Vk5vZGUgYXMgX2NyZWF0ZUVsZW1lbnRWTm9kZSwgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrIH0gZnJvbSAidnVlIjsKY29uc3QgX2hvaXN0ZWRfMSA9IHsKICBzdHlsZTogewogICAgIndpZHRoIjogIjEwMCUiLAogICAgImxpbmUtaGVpZ2h0IjogIjMwcHgiLAogICAgInRleHQtYWxpZ24iOiAibGVmdCIKICB9Cn07CmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgcmV0dXJuIF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgX2hvaXN0ZWRfMSwgW19jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIHsKICAgIGNsYXNzOiAiZWNoYXJ0IiwKICAgIGlkOiAibXljaGFydCIsCiAgICBzdHlsZTogX25vcm1hbGl6ZVN0eWxlKCRkYXRhLm15Q2hhcnRTdHlsZSkKICB9LCBudWxsLCA0IC8qIFNUWUxFICovKV0pOwp9"}, {"version": 3, "names": ["style", "_createElementBlock", "_hoisted_1", "_createElementVNode", "class", "id", "_normalizeStyle", "$data", "myChartStyle"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total1.vue"], "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <div class=\"echart\" id=\"mychart\" :style=\"myChartStyle\"></div>\n \r\n    </div>\r\n</template>\r\n\r\n<script>\r\n\n import request, { base } from \"../../../../utils/http\";\n import * as echarts from \"echarts\";\n \n export default {\n   data() {\n     return {\n       myChart: {},\n       pieData: [\n       ],\n       pieName: [],\n       myChartStyle: { float: \"left\", width: \"100%\", height: \"550px\" } //图表样式\n     };\n   },\n   mounted() {\n     this.getdata();\n   },\n   methods: {\n \n     //数据初始化\n     getdata() {\n       let url = base + \"/ReportData/queryReport\";\n \n       let para = {\n       };\n \n       request.post(url, para).then((res) => {\n         if (res.code == 200) {\n           var ss = res.resdata;\n \n           for (let i = 0; i < ss.length; i++) {\n             this.pieData[i] = {\n               name: ss[i].name,\n               value: ss[i].num\n             };\n \n             this.initDate();\n             this.initEcharts();\n           }\n         } else {\n           this.$message.error(res.msg);\n         }\n       });\n \n     },\n \n \n     initDate() {\n       for (let i = 0; i < this.pieData.length; i++) {\n         this.pieName[i] = this.pieData[i].name;\n       }\n     },\n     initEcharts() {\n       // 饼图\n       const option = {\n         legend: {\n           // 图例\n           data: this.pieName,\n           right: \"10%\",\n           top: \"10%\",\n           orient: \"vertical\"\n         },\n         title: {\n \n           text: \"产品分类销售统计\",\n           top: \"10%\",\n           left: \"center\"\n         },\n         series: [\n           {\n             type: \"pie\",\n             label: {\n               show: true,\n               formatter: \"{b} : {c} ({d}%)\" // b代表名称，c代表对应值，d代表百分比\n             },\n             radius: \"30%\", //饼图半径\n             data: this.pieData\n           }\n         ]\n       };\n       console.log(this.seriesData);\n       const optionFree = {\n         xAxis: {},\n         yAxis: {},\n         series: [\n           {\n             data: this.seriesData,\n             type: \"line\",\n             smooth: true\n           }\n         ]\n       };\n       this.myChart = echarts.init(document.getElementById(\"mychart\"));\n       this.myChart.setOption(option);\n       //随着屏幕大小调节图表\n       window.addEventListener(\"resize\", () => {\n         this.myChart.resize();\n       });\n     }\n   }\n };\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";;EACSA,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;;uBAA5DC,mBAAA,CAGM,OAHNC,UAGM,GAFHC,mBAAA,CAA6D;IAAxDC,KAAK,EAAC,QAAQ;IAACC,EAAE,EAAC,SAAS;IAAEL,KAAK,EAAAM,eAAA,CAAEC,KAAA,CAAAC,YAAY", "ignoreList": []}]}