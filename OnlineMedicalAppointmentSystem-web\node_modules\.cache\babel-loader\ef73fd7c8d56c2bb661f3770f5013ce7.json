{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total2.vue?vue&type=template&id=75fa3093", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total2.vue", "mtime": 1749221490972}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY3JlYXRlQ29tbWVudFZOb2RlIGFzIF9jcmVhdGVDb21tZW50Vk5vZGUsIGNyZWF0ZUVsZW1lbnRWTm9kZSBhcyBfY3JlYXRlRWxlbWVudFZOb2RlLCBjcmVhdGVUZXh0Vk5vZGUgYXMgX2NyZWF0ZVRleHRWTm9kZSwgcmVzb2x2ZUNvbXBvbmVudCBhcyBfcmVzb2x2ZUNvbXBvbmVudCwgd2l0aEN0eCBhcyBfd2l0aEN0eCwgY3JlYXRlVk5vZGUgYXMgX2NyZWF0ZVZOb2RlLCBvcGVuQmxvY2sgYXMgX29wZW5CbG9jaywgY3JlYXRlRWxlbWVudEJsb2NrIGFzIF9jcmVhdGVFbGVtZW50QmxvY2sgfSBmcm9tICJ2dWUiOwpjb25zdCBfaG9pc3RlZF8xID0gewogIHN0eWxlOiB7CiAgICAid2lkdGgiOiAiMTAwJSIsCiAgICAibGluZS1oZWlnaHQiOiAiMzBweCIsCiAgICAidGV4dC1hbGlnbiI6ICJsZWZ0IgogIH0KfTsKY29uc3QgX2hvaXN0ZWRfMiA9IHsKICBzdHlsZTogewogICAgIm1hcmdpbi1ib3R0b20iOiAiMjBweCIsCiAgICAidGV4dC1hbGlnbiI6ICJyaWdodCIKICB9Cn07CmV4cG9ydCBmdW5jdGlvbiByZW5kZXIoX2N0eCwgX2NhY2hlLCAkcHJvcHMsICRzZXR1cCwgJGRhdGEsICRvcHRpb25zKSB7CiAgY29uc3QgX2NvbXBvbmVudF9lbF9idXR0b24gPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtYnV0dG9uIik7CiAgcmV0dXJuIF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgX2hvaXN0ZWRfMSwgW19jcmVhdGVDb21tZW50Vk5vZGUoIiDmk43kvZzmjInpkq4gIiksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIF9ob2lzdGVkXzIsIFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9idXR0b24sIHsKICAgIHR5cGU6ICJwcmltYXJ5IiwKICAgIG9uQ2xpY2s6IF9jdHguZXhwb3J0VG9FeGNlbCwKICAgIGxvYWRpbmc6ICRkYXRhLmV4cG9ydExvYWRpbmcKICB9LCB7CiAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBfY2FjaGVbMF0gfHwgKF9jYWNoZVswXSA9IFtfY3JlYXRlRWxlbWVudFZOb2RlKCJpIiwgewogICAgICBjbGFzczogImVsLWljb24tZG93bmxvYWQiCiAgICB9LCBudWxsLCAtMSAvKiBIT0lTVEVEICovKSwgX2NyZWF0ZVRleHRWTm9kZSgiIOWvvOWHukV4Y2VsICIpXSkpLAogICAgXzogMSAvKiBTVEFCTEUgKi8sCiAgICBfXzogWzBdCiAgfSwgOCAvKiBQUk9QUyAqLywgWyJvbkNsaWNrIiwgImxvYWRpbmciXSldKSwgX2NyZWF0ZUNvbW1lbnRWTm9kZSgiIOWbvuihqOWuueWZqCAiKSwgX2NhY2hlWzFdIHx8IChfY2FjaGVbMV0gPSBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCB7CiAgICBpZDogImNvbnRhaW5lciIsCiAgICBzdHlsZTogewogICAgICBoZWlnaHQ6ICc2MDBweCcKICAgIH0KICB9LCBudWxsLCAtMSAvKiBIT0lTVEVEICovKSldKTsKfQ=="}, {"version": 3, "names": ["style", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_button", "type", "onClick", "_ctx", "exportToExcel", "loading", "$data", "exportLoading", "_cache", "class", "id", "height"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total2.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n        <!-- 操作按钮 -->\n        <div style=\"margin-bottom: 20px; text-align: right;\">\n            <el-button type=\"primary\" @click=\"exportToExcel\" :loading=\"exportLoading\">\n                <i class=\"el-icon-download\"></i> 导出Excel\n            </el-button>\n        </div>\n\n        <!-- 图表容器 -->\n        <div id=\"container\" :style=\"{ height: '600px' }\"></div>\n    </div>\n</template>\n\n<script>\n\n import request, { base } from \"../../../../utils/http\";\n import * as echarts from 'echarts';\n import * as XLSX from 'xlsx';\n import { saveAs } from 'file-saver';\n\n export default {\n   data() {\n     return {\n       pieData: [],\n       pieName: [],\n       exportLoading: false,\n       myChart: null\n     };\n   },\n \n   mounted() {\n     this.getdata();\n     window.addEventListener('resize', this.resizeChart);\n   },\n   beforeDestroy() {\n     window.removeEventListener('resize', this.resizeChart);\n   },\n   methods: {\n     // 数据初始化\n     getdata() {\n       let url = base + \"/ReportData/queryReport2\";\n \n       let para = {};\n \n       request.post(url, para).then((res) => {\n         if (res.code == 200) {\n           var ss = res.resdata;\n           var pieName2 = [];\n           var pieData2 = [];\n           for (let i = 0; i < ss.length; i++) {\n             pieName2[i] = ss[i].name;\n             pieData2[i] = ss[i].num;\n           }\n           this.pieName = pieName2;\n           this.pieData = pieData2;\n           this.initEcharts();\n         } else {\n           this.$message.error(res.msg);\n         }\n       });\n     },\n \n     initEcharts() {\n       const dom = document.getElementById('container');\n       const myChart = echarts.init(dom, null, { renderer: 'canvas' });\n \n       const option = {\n         title: {\n \n         },\n         tooltip: {\n           trigger: 'axis',\n           axisPointer: {\n             type: 'shadow'\n           }\n         },\n         legend: {},\n         grid: {\n           left: '3%',\n           right: '4%',\n           bottom: '3%',\n           containLabel: true\n         },\n         xAxis: {\n           type: 'value',\n           boundaryGap: [0, 0.01]\n         },\n         yAxis: {\n           type: 'category',\n           data: this.pieName\n         },\n         series: [\n           {\n             name: '总销售数量',\n             type: 'bar',\n             data: this.pieData\n           }\n         ]\n       };\n \n       if (option && typeof option === 'object') {\n         myChart.setOption(option);\n       }\n     },\n     resizeChart() {\n       const dom = document.getElementById('container');\n       const myChart = echarts.getInstanceByDom(dom);\n       myChart.resize();\n     }\n   }\n };\n</script>\n<style scoped>\n</style>\n \n\n"], "mappings": ";;EACSA,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;;EAEnDA,KAA+C,EAA/C;IAAA;IAAA;EAAA;AAA+C;;;uBAFxDC,mBAAA,CAUM,OAVNC,UAUM,GATFC,mBAAA,UAAa,EACbC,mBAAA,CAIM,OAJNC,UAIM,GAHFC,YAAA,CAEYC,oBAAA;IAFDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEC,IAAA,CAAAC,aAAa;IAAGC,OAAO,EAAEC,KAAA,CAAAC;;sBACvD,MAAgCC,MAAA,QAAAA,MAAA,OAAhCX,mBAAA,CAAgC;MAA7BY,KAAK,EAAC;IAAkB,4B,iBAAK,WACpC,E;;;+CAGJb,mBAAA,UAAa,E,0BACbC,mBAAA,CAAuD;IAAlDa,EAAE,EAAC,WAAW;IAAEjB,KAAK,EAAE;MAAAkB,MAAA;IAAA", "ignoreList": []}]}