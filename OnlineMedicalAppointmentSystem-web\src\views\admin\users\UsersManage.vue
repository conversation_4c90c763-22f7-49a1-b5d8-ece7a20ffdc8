﻿<template>
  <div style="width: 100%;line-height: 30px;text-align: left;">
    <el-col :span="24" style="padding-bottom: 0px; margin-left: 10px">
      <el-form :inline="true" :model="filters">
        <el-form-item>
          <el-input v-model="filters.lname" placeholder="用户名" size="small"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="filters.uname" placeholder="姓名" size="small"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="filters.uphone" placeholder="手机号码" size="small"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="query" icon="el-icon-search">搜索</el-button>
        </el-form-item>
      </el-form>
    </el-col>

    <el-table :data="datalist" border stripe style="width: 100%" v-loading="listLoading" highlight-current-row
      max-height="600" size="small">
      <el-table-column prop="lname" label="用户名" align="center"></el-table-column>
      <el-table-column prop="upassword" label="登录密码" align="center"></el-table-column>
      <el-table-column prop="uname" label="姓名" align="center"></el-table-column>
      <el-table-column prop="usex" label="性别" align="center"></el-table-column>
      <el-table-column prop="uphone" label="手机号码" align="center"></el-table-column>
      <el-table-column prop="pic" label="照片" width="70" align="center">
        <template #default="scope">
          <img :src="'http://localhost:8088/OnlineMedicalAppointmentSystem_Server/upload/' + scope.row.pic"
            style="width: 50px;height: 50px" />
        </template>
      </el-table-column>
      <el-table-column prop="utime" label="注册时间" align="center"></el-table-column>
      <el-table-column label="操作" min-width="200" align="center">
        <template #default="scope">
          <el-button type="primary" size="mini" @click="handleShow(scope.$index, scope.row)" icon="el-icon-zoom-in"
            style=" padding: 3px 6px 3px 6px;">详情</el-button>
          <el-button type="success" size="mini" @click="handleEdit(scope.$index, scope.row)" icon="el-icon-edit"
            style=" padding: 3px 6px 3px 6px;">编辑</el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.$index, scope.row)" icon="el-icon-delete"
            style=" padding: 3px 6px 3px 6px;">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @current-change="handleCurrentChange" :current-page="page.currentPage" :page-size="page.pageSize"
      background layout="total, prev, pager, next, jumper" :total="page.totalCount"
      style="float: right; margin: 10px 20px 0 0"></el-pagination>

  </div>
</template>

<script>
import request, { base } from "../../../../utils/http";
export default {
  name: 'users',
  components: {

  },
  data() {
    return {
      filters: {
        //列表查询参数
        lname: '',
        uname: '',
        uphone: '',
      },

      page: {
        currentPage: 1, // 当前页
        pageSize: 10, // 每页显示条目个数
        totalCount: 0, // 总条目数
      },
      isClear: false,

      listLoading: false, //列表加载状态
      btnLoading: false, //保存按钮加载状态
      datalist: [], //表格数据  

    };
  },
  created() {
    this.getDatas();
  },


  methods: {


    // 删除患者
    handleDelete(index, row) {
      this.$confirm("确认删除该记录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.listLoading = true;
          let url = base + "/users/del?id=" + row.lname;
          request.post(url).then((res) => {
            this.listLoading = false;

            this.$message({
              message: "删除成功",
              type: "success",
              offset: 320,
            });
            this.getDatas();
          });
        })
        .catch(() => { });
    },

    // 分页
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.getDatas();
    },

    //获取列表数据
    getDatas() {
      let para = {
        lname: this.filters.lname,
        uname: this.filters.uname,
        uphone: this.filters.uphone,

      };
      this.listLoading = true;
      let url = base + "/users/list?currentPage=" + this.page.currentPage + "&pageSize=" + this.page.pageSize;
      request.post(url, para).then((res) => {
        if (res.resdata.length > 0) {
          this.isPage = true;
        } else {
          this.isPage = false;
        }
        this.page.totalCount = res.count;
        this.datalist = res.resdata;
        this.listLoading = false;
      }).catch((error) => {
        console.error('获取用户列表失败:', error);
        this.listLoading = false;
        this.$message.error('获取用户列表失败');
      });
    },
    //查询
    query() {
      this.getDatas();
    },

    // 查看
    handleShow(index, row) {
      this.$router.push({
        path: "/UsersDetail",
        query: {
          id: row.lname,
        },
      });
    },

    // 编辑
    handleEdit(index, row) {
      this.$router.push({
        path: "/UsersEdit",
        query: {
          id: row.lname,
        },
      });
    },
  },
}

</script>
<style scoped></style>
