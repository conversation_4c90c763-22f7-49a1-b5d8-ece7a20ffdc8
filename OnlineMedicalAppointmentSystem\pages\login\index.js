const App = getApp();
Page({
  data: {},

  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
      });
    }
  },

  //页面显示
  onShow() {
    this.getForm1();
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },

  //设置输入验证
  getForm1() {
    this.WxValidate = App.WxValidate({
      lname: {
        required: {
          message: "用户名：不能为空哟",
        },
      },
      pass: {
        required: {
          message: "登录密码：不能为空哟",
        },
      },
    });
  },

  //登录
  submitForm: function (e) {
    if (!this.WxValidate.checkForm(e)) {
      //输入验证不通过时，弹出相应的提示信息，并返回
      const error = this.WxValidate.errorList[0];
      var msg = `${error.msg}`;
      App.showToast(msg, "none");
      return false;
    } else {
      var data = e.detail.value;

      data = App.Tools.extend(data, {
        f: 1,
      });

      //调用服务器端登录servlet，并根据返回结果，执行相应的操作
      data.upassword = data.pass;
      App.HttpService.saveData(data, "/users/login2.action").then((data) => {
        if (data.code == 200) {
          App.WxService.showToast({
            title: "登陆成功",
            icon: "success",
            duration: 1500,
          });
          setTimeout(function () {
            wx.setStorageSync("lname", e.detail.value.lname); //存储当前登录的用户名
            wx.switchTab({
              url: "/pages/index/index", //跳转到首页
            });
          }, 1500);
        } else {
          App.WxService.showToast({
            title: "用户名或密码错误，请重试",
            icon: "none",
            duration: 1500,
          });
        }
      });
    }
  },
});
