<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.UsersMapper">
	<select id="findUsersList"  resultType="Users">
		select * from users 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Users">
	    select  *  
        from users a  	
		<where>
      		<if test="lname != null and lname != ''">
		    and a.lname = #{lname}
		</if>
		<if test="upassword != null and upassword != ''">
		    and a.upassword = #{upassword}
		</if>
		<if test="uname != null and uname != ''">
		    and a.uname = #{uname}
		</if>
		<if test="usex != null and usex != ''">
		    and a.usex = #{usex}
		</if>
		<if test="uphone != null and uphone != ''">
		    and a.uphone = #{uphone}
		</if>
		<if test="address != null and address != ''">
		    and a.address = #{address}
		</if>
		<if test="pic != null and pic != ''">
		    and a.pic = #{pic}
		</if>
		<if test="utime != null and utime != ''">
		    and a.utime = #{utime}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} utime desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from users a  
		<where>
      		<if test="lname != null and lname != ''">
		    and a.lname = #{lname}
		</if>
		<if test="upassword != null and upassword != ''">
		    and a.upassword = #{upassword}
		</if>
		<if test="uname != null and uname != ''">
		    and a.uname = #{uname}
		</if>
		<if test="usex != null and usex != ''">
		    and a.usex = #{usex}
		</if>
		<if test="uphone != null and uphone != ''">
		    and a.uphone = #{uphone}
		</if>
		<if test="address != null and address != ''">
		    and a.address = #{address}
		</if>
		<if test="pic != null and pic != ''">
		    and a.pic = #{pic}
		</if>
		<if test="utime != null and utime != ''">
		    and a.utime = #{utime}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="queryUsersById" parameterType="String" resultType="Users">
    select  *  
     from users a  	 where a.lname=#{value}
  </select>
 
	<insert id="insertUsers" useGeneratedKeys="true" keyProperty="lname" parameterType="Users">
    insert into users
    (lname,upassword,uname,usex,uphone,address,pic,utime)
    values
    (#{lname},#{upassword},#{uname},#{usex},#{uphone},#{address},#{pic},now());
  </insert>
	
	<update id="updateUsers" parameterType="Users" >
    update users 
    <set>
		<if test="lname != null and lname != ''">
		    lname = #{lname},
		</if>
		<if test="upassword != null and upassword != ''">
		    upassword = #{upassword},
		</if>
		<if test="uname != null and uname != ''">
		    uname = #{uname},
		</if>
		<if test="usex != null and usex != ''">
		    usex = #{usex},
		</if>
		<if test="uphone != null and uphone != ''">
		    uphone = #{uphone},
		</if>
		<if test="address != null and address != ''">
		    address = #{address},
		</if>
		<if test="pic != null and pic != ''">
		    pic = #{pic},
		</if>
		<if test="utime != null and utime != ''">
		    utime = #{utime},
		</if>

    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="lname != null or lname != ''">
      lname=#{lname}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteUsers" parameterType="String">
    delete from  users where lname=#{value}
  </delete>

	
	
</mapper>

 
