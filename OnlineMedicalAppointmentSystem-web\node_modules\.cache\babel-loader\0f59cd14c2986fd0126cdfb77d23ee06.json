{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\plans\\PlansAdd.vue?vue&type=template&id=a0e80854", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\plans\\PlansAdd.vue", "mtime": 1749198848639}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "model", "$data", "formData", "ref", "rules", "add<PERSON><PERSON>", "align", "_component_el_form_item", "label", "prop", "_component_el_select", "did", "$event", "placeholder", "size", "_Fragment", "_renderList", "doctorList", "item", "_createBlock", "_component_el_option", "key", "dname", "value", "weeks", "ptime", "_component_el_input", "people", "_component_el_button", "type", "onClick", "$options", "save", "loading", "btnLoading", "icon", "_cache", "goBack"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\plans\\PlansAdd.vue"], "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\"  align=\"left\">\r\n<el-form-item label=\"医生\" prop=\"did\">\r\n<el-select v-model=\"formData.did\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option v-for=\"item in doctorList\" :key=\"item.did\" :label=\"item.dname\" :value=\"item.did\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"星期\" prop=\"weeks\">\r\n<el-select v-model=\"formData.weeks\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option label=\"星期一\" value=\"星期一\"></el-option>\r\n<el-option label=\"星期二\" value=\"星期二\"></el-option>\r\n<el-option label=\"星期三\" value=\"星期三\"></el-option>\r\n<el-option label=\"星期四\" value=\"星期四\"></el-option>\r\n<el-option label=\"星期五\" value=\"星期五\"></el-option>\r\n<el-option label=\"星期六\" value=\"星期六\"></el-option>\r\n<el-option label=\"星期日\" value=\"星期日\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"时间段\" prop=\"ptime\">\r\n<el-select v-model=\"formData.ptime\" placeholder=\"请选择\"  size=\"small\">\r\n<el-option label=\"8:00-9:00\" value=\"8:00-9:00\"></el-option>\r\n<el-option label=\"9:00-10:00\" value=\"9:00-10:00\"></el-option>\r\n<el-option label=\"10:00-11:00\" value=\"10:00-11:00\"></el-option>\r\n<el-option label=\"11:00-12:00\" value=\"11:00-12:00\"></el-option>\r\n<el-option label=\"14:00-15:00\" value=\"14:00-15:00\"></el-option>\r\n<el-option label=\"15:00-16:00\" value=\"15:00-16:00\"></el-option>\r\n<el-option label=\"16:00-17:00\" value=\"16:00-17:00\"></el-option>\r\n</el-select>\r\n</el-form-item>\r\n<el-form-item label=\"号数\" prop=\"people\">\r\n<el-input v-model=\"formData.people\" placeholder=\"号数\"  style=\"width:50%;\" ></el-input>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n<el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../../utils/http\";\r\n\r\nexport default {\r\n  name: 'PlansAdd',\r\n  components: {\r\n    \r\n  },  \r\n    data() {\r\n      return {\r\n        uploadVisible: false,\r\n        btnLoading: false, //保存按钮加载状态\r\n        formData: {}, //表单数据\r\n        doctorList: [], //医生列表\r\n        listLoading: false, //列表加载状态\r\n        addrules: {\r\n          did: [{ required: true, message: '请选择医生', trigger: 'onchange' }],\r\n          weeks: [{ required: true, message: '请选择星期', trigger: 'onchange' }],\r\n          ptime: [{ required: true, message: '请选择时间段', trigger: 'onchange' }],\r\n          people: [{ required: true, message: '请输入号数', trigger: 'blur' },\r\n],        },\r\n\r\n      };\r\n    },\r\n    mounted() {\r\n    \r\n      this.getdoctorList();\r\n    },\r\n\r\n \r\n    methods: {    \r\n   // 添加\r\n    save() {       \r\n         this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\r\n           if (valid) {\r\n             let url = base + \"/plans/add\";\r\n             this.btnLoading = true;\r\n             request.post(url, this.formData).then((res) => { //发送请求         \r\n               if (res.code == 200) {\r\n                 this.$message({\r\n                   message: \"操作成功\",\r\n                   type: \"success\",\r\n                   offset: 320,\r\n                 });              \r\n                this.$router.push({\r\n                path: \"/PlansManage\",\r\n                });\r\n               } else {\r\n                 this.$message({\r\n                   message: res.msg,\r\n                   type: \"error\",\r\n                   offset: 320,\r\n                 });\r\n               }\r\n               this.btnLoading=false;\r\n             });\r\n           }        \r\n           \r\n         });\r\n    },\r\n    \r\n       // 返回\r\n        goBack() {\r\n          this.$router.push({\r\n            path: \"/PlansManage\",\r\n          });\r\n        },       \r\n              \r\n            \r\n    getdoctorList() {\r\n      let para = {\r\n        sort: \"a.\" // 添加必需的sort字段\r\n      };\r\n      this.listLoading = true;\r\n      let url = base + \"/doctor/list?currentPage=1&pageSize=1000\";\r\n      request.post(url, para).then((res) => {\r\n        this.doctorList = res.resdata;\r\n        this.listLoading = false;\r\n      }).catch((error) => {\r\n        console.error('获取医生列表失败:', error);\r\n        this.listLoading = false;\r\n        this.$message.error('获取医生列表失败');\r\n      });\r\n    },\r\n  \r\n           \r\n           \r\n      },\r\n}\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": ";;EACSA,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;;;;;;;;uBAA5DC,mBAAA,CAuCM,OAvCNC,UAuCM,GAtCHC,YAAA,CAmCGC,kBAAA;IAnCOC,KAAK,EAAEC,KAAA,CAAAC,QAAQ;IAAE,aAAW,EAAC,KAAK;IAACC,GAAG,EAAC,aAAa;IAAEC,KAAK,EAAEH,KAAA,CAAAI,QAAQ;IAAGC,KAAK,EAAC;;sBAC/F,MAIe,CAJfR,YAAA,CAIeS,uBAAA;MAJDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAEY,CAFZX,YAAA,CAEYY,oBAAA;oBAFQT,KAAA,CAAAC,QAAQ,CAACS,GAAG;mEAAZV,KAAA,CAAAC,QAAQ,CAACS,GAAG,GAAAC,MAAA;QAAEC,WAAW,EAAC,KAAK;QAAEC,IAAI,EAAC;;0BAC/C,MAA0B,E,kBAArClB,mBAAA,CAAwGmB,SAAA,QAAAC,WAAA,CAA9Ef,KAAA,CAAAgB,UAAU,EAAlBC,IAAI;+BAAtBC,YAAA,CAAwGC,oBAAA;YAAjEC,GAAG,EAAEH,IAAI,CAACP,GAAG;YAAGH,KAAK,EAAEU,IAAI,CAACI,KAAK;YAAGC,KAAK,EAAEL,IAAI,CAACP;;;;;;QAGvFb,YAAA,CAUeS,uBAAA;MAVDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAQY,CARZX,YAAA,CAQYY,oBAAA;oBARQT,KAAA,CAAAC,QAAQ,CAACsB,KAAK;mEAAdvB,KAAA,CAAAC,QAAQ,CAACsB,KAAK,GAAAZ,MAAA;QAAEC,WAAW,EAAC,KAAK;QAAEC,IAAI,EAAC;;0BAC5D,MAA+C,CAA/ChB,YAAA,CAA+CsB,oBAAA;UAApCZ,KAAK,EAAC,KAAK;UAACe,KAAK,EAAC;YAC7BzB,YAAA,CAA+CsB,oBAAA;UAApCZ,KAAK,EAAC,KAAK;UAACe,KAAK,EAAC;YAC7BzB,YAAA,CAA+CsB,oBAAA;UAApCZ,KAAK,EAAC,KAAK;UAACe,KAAK,EAAC;YAC7BzB,YAAA,CAA+CsB,oBAAA;UAApCZ,KAAK,EAAC,KAAK;UAACe,KAAK,EAAC;YAC7BzB,YAAA,CAA+CsB,oBAAA;UAApCZ,KAAK,EAAC,KAAK;UAACe,KAAK,EAAC;YAC7BzB,YAAA,CAA+CsB,oBAAA;UAApCZ,KAAK,EAAC,KAAK;UAACe,KAAK,EAAC;YAC7BzB,YAAA,CAA+CsB,oBAAA;UAApCZ,KAAK,EAAC,KAAK;UAACe,KAAK,EAAC;;;;;QAG7BzB,YAAA,CAUeS,uBAAA;MAVDC,KAAK,EAAC,KAAK;MAACC,IAAI,EAAC;;wBAC/B,MAQY,CARZX,YAAA,CAQYY,oBAAA;oBARQT,KAAA,CAAAC,QAAQ,CAACuB,KAAK;mEAAdxB,KAAA,CAAAC,QAAQ,CAACuB,KAAK,GAAAb,MAAA;QAAEC,WAAW,EAAC,KAAK;QAAEC,IAAI,EAAC;;0BAC5D,MAA2D,CAA3DhB,YAAA,CAA2DsB,oBAAA;UAAhDZ,KAAK,EAAC,WAAW;UAACe,KAAK,EAAC;YACnCzB,YAAA,CAA6DsB,oBAAA;UAAlDZ,KAAK,EAAC,YAAY;UAACe,KAAK,EAAC;YACpCzB,YAAA,CAA+DsB,oBAAA;UAApDZ,KAAK,EAAC,aAAa;UAACe,KAAK,EAAC;YACrCzB,YAAA,CAA+DsB,oBAAA;UAApDZ,KAAK,EAAC,aAAa;UAACe,KAAK,EAAC;YACrCzB,YAAA,CAA+DsB,oBAAA;UAApDZ,KAAK,EAAC,aAAa;UAACe,KAAK,EAAC;YACrCzB,YAAA,CAA+DsB,oBAAA;UAApDZ,KAAK,EAAC,aAAa;UAACe,KAAK,EAAC;YACrCzB,YAAA,CAA+DsB,oBAAA;UAApDZ,KAAK,EAAC,aAAa;UAACe,KAAK,EAAC;;;;;QAGrCzB,YAAA,CAEeS,uBAAA;MAFDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;wBAC9B,MAAqF,CAArFX,YAAA,CAAqF4B,mBAAA;oBAAlEzB,KAAA,CAAAC,QAAQ,CAACyB,MAAM;mEAAf1B,KAAA,CAAAC,QAAQ,CAACyB,MAAM,GAAAf,MAAA;QAAEC,WAAW,EAAC,IAAI;QAAElB,KAAkB,EAAlB;UAAA;QAAA;;;QAEtDG,YAAA,CAGeS,uBAAA;wBAFf,MAAgH,CAAhHT,YAAA,CAAgH8B,oBAAA;QAArGC,IAAI,EAAC,SAAS;QAACf,IAAI,EAAC,OAAO;QAAEgB,OAAK,EAAEC,QAAA,CAAAC,IAAI;QAAGC,OAAO,EAAEhC,KAAA,CAAAiC,UAAU;QAAEC,IAAI,EAAC;;0BAAiB,MAAGC,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;;iDACpGtC,YAAA,CAAuF8B,oBAAA;QAA5EC,IAAI,EAAC,MAAM;QAACf,IAAI,EAAC,OAAO;QAAEgB,OAAK,EAAEC,QAAA,CAAAM,MAAM;QAAEF,IAAI,EAAC;;0BAAe,MAAGC,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E", "ignoreList": []}]}