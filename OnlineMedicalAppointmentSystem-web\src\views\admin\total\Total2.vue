﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <div id="container" :style="{ height: '600px' }"></div>
 
    </div>
</template>

<script>

 import request, { base } from "../../../../utils/http";
 import * as echarts from 'echarts';
 
 export default {
   data() {
     return {
       pieData: [],
       pieName: [],
     };
   },
 
   mounted() {
     this.getdata();
     window.addEventListener('resize', this.resizeChart);
   },
   beforeDestroy() {
     window.removeEventListener('resize', this.resizeChart);
   },
   methods: {
     // 数据初始化
     getdata() {
       let url = base + "/ReportData/queryReport2";
 
       let para = {};
 
       request.post(url, para).then((res) => {
         if (res.code == 200) {
           var ss = res.resdata;
           var pieName2 = [];
           var pieData2 = [];
           for (let i = 0; i < ss.length; i++) {
             pieName2[i] = ss[i].name;
             pieData2[i] = ss[i].num;
           }
           this.pieName = pieName2;
           this.pieData = pieData2;
           this.initEcharts();
         } else {
           this.$message.error(res.msg);
         }
       });
     },
 
     initEcharts() {
       const dom = document.getElementById('container');
       const myChart = echarts.init(dom, null, { renderer: 'canvas' });
 
       const option = {
         title: {
 
         },
         tooltip: {
           trigger: 'axis',
           axisPointer: {
             type: 'shadow'
           }
         },
         legend: {},
         grid: {
           left: '3%',
           right: '4%',
           bottom: '3%',
           containLabel: true
         },
         xAxis: {
           type: 'value',
           boundaryGap: [0, 0.01]
         },
         yAxis: {
           type: 'category',
           data: this.pieName
         },
         series: [
           {
             name: '总销售数量',
             type: 'bar',
             data: this.pieData
           }
         ]
       };
 
       if (option && typeof option === 'object') {
         myChart.setOption(option);
       }
     },
     resizeChart() {
       const dom = document.getElementById('container');
       const myChart = echarts.getInstanceByDom(dom);
       myChart.resize();
     }
   }
 };
</script>
<style scoped>
</style>
 

