﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
        <!-- 操作按钮 -->
        <div style="margin-bottom: 20px; text-align: right;">
            <el-button type="primary" @click="exportToExcel" :loading="exportLoading">
                <i class="el-icon-download"></i> 导出Excel
            </el-button>
        </div>

        <!-- 图表容器 -->
        <div id="container" :style="{ height: '600px' }"></div>
    </div>
</template>

<script>

 import request, { base } from "../../../../utils/http";
 import * as echarts from 'echarts';
 import * as XLSX from 'xlsx';
 import { saveAs } from 'file-saver';

 export default {
   data() {
     return {
       pieData: [],
       pieName: [],
       exportLoading: false,
       myChart: null
     };
   },
 
   mounted() {
     this.getdata();
     window.addEventListener('resize', this.resizeChart);
   },
   beforeDestroy() {
     window.removeEventListener('resize', this.resizeChart);
   },
   methods: {
     // 数据初始化
     getdata() {
       let url = base + "/ReportData/queryReport2";
 
       let para = {};
 
       request.post(url, para).then((res) => {
         if (res.code == 200) {
           var ss = res.resdata;
           var pieName2 = [];
           var pieData2 = [];
           for (let i = 0; i < ss.length; i++) {
             pieName2[i] = ss[i].name;
             pieData2[i] = ss[i].num;
           }
           this.pieName = pieName2;
           this.pieData = pieData2;
           this.initEcharts();
         } else {
           this.$message.error(res.msg);
         }
       });
     },
 
     initEcharts() {
       const dom = document.getElementById('container');
       this.myChart = echarts.init(dom, null, { renderer: 'canvas' });

       const option = {
         title: {
           text: '医生预约统计',
           left: 'center'
         },
         tooltip: {
           trigger: 'axis',
           axisPointer: {
             type: 'shadow'
           },
           formatter: '{a} <br/>{b}: {c}'
         },
         legend: {
           data: ['预约数量']
         },
         grid: {
           left: '3%',
           right: '4%',
           bottom: '3%',
           containLabel: true
         },
         xAxis: {
           type: 'value',
           boundaryGap: [0, 0.01]
         },
         yAxis: {
           type: 'category',
           data: this.pieName
         },
         series: [
           {
             name: '预约数量',
             type: 'bar',
             data: this.pieData,
             itemStyle: {
               color: '#409EFF'
             }
           }
         ]
       };

       if (option && typeof option === 'object') {
         this.myChart.setOption(option);
       }
     },

     resizeChart() {
       if (this.myChart) {
         this.myChart.resize();
       }
     },

     // Excel导出功能
     exportToExcel() {
       this.exportLoading = true;

       try {
         // 准备导出数据
         const exportData = this.pieName.map((name, index) => ({
           '序号': index + 1,
           '医生姓名': name,
           '预约数量': this.pieData[index] || 0
         }));

         // 创建工作簿
         const ws = XLSX.utils.json_to_sheet(exportData);
         const wb = XLSX.utils.book_new();
         XLSX.utils.book_append_sheet(wb, ws, "医生预约统计");

         // 设置列宽
         ws['!cols'] = [
           { wch: 8 },  // 序号
           { wch: 20 }, // 医生姓名
           { wch: 12 }  // 预约数量
         ];

         // 导出文件
         const fileName = `医生预约统计_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`;
         XLSX.writeFile(wb, fileName);

         this.$message.success('导出成功！');
       } catch (error) {
         console.error('导出失败:', error);
         this.$message.error('导出失败，请重试');
       } finally {
         this.exportLoading = false;
       }
     }
   }
 };
</script>
<style scoped>
</style>
 

