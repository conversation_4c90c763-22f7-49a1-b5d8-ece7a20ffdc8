package com.mapper;

/**
 * 统计数据映射接口
 */
public interface StatisticsMapper {
    
    /**
     * 获取患者总数
     */
    int getPatientCount();
    
    /**
     * 获取医生总数
     */
    int getDoctorCount();
    
    /**
     * 获取今日挂号总数
     */
    int getTodayAppointmentCount();
    
    /**
     * 获取总挂号数
     */
    int getTotalAppointmentCount();
    
    /**
     * 获取指定医生今日挂号数
     */
    int getDoctorTodayAppointmentCount(Integer doctorId);
    
    /**
     * 获取指定医生总挂号数
     */
    int getDoctorTotalAppointmentCount(Integer doctorId);
}
