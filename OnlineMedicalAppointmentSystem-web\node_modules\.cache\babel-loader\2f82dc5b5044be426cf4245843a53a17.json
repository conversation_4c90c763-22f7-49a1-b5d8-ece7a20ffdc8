{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\router\\index.js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\router\\index.js", "mtime": 1749221193871}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["createRouter", "createWebHistory", "routes", "path", "name", "component", "meta", "requireAuth", "redirect", "children", "title", "requiresAuth", "router", "history", "process", "env", "BASE_URL", "beforeEach", "to", "from", "next", "sessionStorage", "removeItem", "currentUser", "getItem", "console", "log"], "sources": ["I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\r\n\r\nconst routes = [\r\n  {\r\n    path: '/',\r\n    name: 'Login',\r\n    component: () => import('../views/Login'),\r\n    meta: {\r\n      requireAuth: false\r\n    }\r\n  },\r\n\r\n  {\r\n    path: '/main',\r\n    name: 'Main',\r\n    component: () => import('../views/Main'),\r\n    redirect: \"/home\",\r\n    children: [\r\n      {\r\n        path: '/home',\r\n        name: 'Home',\r\n        component: () => import('../views/admin/Home'),\r\n        meta: {\r\n          requireAuth: true, title: '首页'\r\n        }\r\n\r\n      },\r\n\r\n      {\r\n        path: '/plansAdd',\r\n        name: 'PlansAdd',\r\n        component: () => import('../views/admin/plans/PlansAdd'),\r\n        meta: { requiresAuth: true, title: '排班添加' }\r\n      },\r\n      {\r\n        path: '/plansEdit',\r\n        name: 'PlansEdit',\r\n        component: () => import('../views/admin/plans/PlansEdit'),\r\n        meta: { requiresAuth: true, title: '排班修改' }\r\n      },\r\n      {\r\n        path: '/plansManage',\r\n        name: 'PlansManage',\r\n        component: () => import('../views/admin/plans/PlansManage'),\r\n        meta: { requiresAuth: true, title: '排班管理' }\r\n      },\r\n      {\r\n        path: '/chatinfoManage',\r\n        name: 'ChatinfoManage',\r\n        component: () => import('../views/admin/chatinfo/ChatinfoManage'),\r\n        meta: { requiresAuth: true, title: '聊天管理' }\r\n      },\r\n      {\r\n        path: '/doctorAdd',\r\n        name: 'DoctorAdd',\r\n        component: () => import('../views/admin/doctor/DoctorAdd'),\r\n        meta: { requiresAuth: true, title: '医生添加' }\r\n      },\r\n      {\r\n        path: '/doctorEdit',\r\n        name: 'DoctorEdit',\r\n        component: () => import('../views/admin/doctor/DoctorEdit'),\r\n        meta: { requiresAuth: true, title: '医生修改' }\r\n      },\r\n      {\r\n        path: '/doctorManage',\r\n        name: 'DoctorManage',\r\n        component: () => import('../views/admin/doctor/DoctorManage'),\r\n        meta: { requiresAuth: true, title: '医生管理' }\r\n      },\r\n      {\r\n        path: '/doctorDetail',\r\n        name: 'DoctorDetail',\r\n        component: () => import('../views/admin/doctor/DoctorDetail'),\r\n        meta: { requiresAuth: true, title: '医生详情' }\r\n      },\r\n      {\r\n        path: '/doctorInfo',\r\n        name: 'DoctorInfo',\r\n        component: () => import('../views/admin/doctor/DoctorInfo'),\r\n        meta: { requiresAuth: true, title: '修改个人信息' }\r\n      },\r\n      {\r\n        path: '/partsAdd',\r\n        name: 'PartsAdd',\r\n        component: () => import('../views/admin/parts/PartsAdd'),\r\n        meta: { requiresAuth: true, title: '科室添加' }\r\n      },\r\n      {\r\n        path: '/partsEdit',\r\n        name: 'PartsEdit',\r\n        component: () => import('../views/admin/parts/PartsEdit'),\r\n        meta: { requiresAuth: true, title: '科室修改' }\r\n      },\r\n      {\r\n        path: '/partsManage',\r\n        name: 'PartsManage',\r\n        component: () => import('../views/admin/parts/PartsManage'),\r\n        meta: { requiresAuth: true, title: '科室管理' }\r\n      },\r\n      {\r\n        path: '/partsDetail',\r\n        name: 'PartsDetail',\r\n        component: () => import('../views/admin/parts/PartsDetail'),\r\n        meta: { requiresAuth: true, title: '科室详情' }\r\n      },\r\n      {\r\n        path: '/usersEdit',\r\n        name: 'UsersEdit',\r\n        component: () => import('../views/admin/users/UsersEdit'),\r\n        meta: { requiresAuth: true, title: '患者修改' }\r\n      },\r\n      {\r\n        path: '/usersManage',\r\n        name: 'UsersManage',\r\n        component: () => import('../views/admin/users/UsersManage'),\r\n        meta: { requiresAuth: true, title: '患者管理' }\r\n      },\r\n      {\r\n        path: '/usersDetail',\r\n        name: 'UsersDetail',\r\n        component: () => import('../views/admin/users/UsersDetail'),\r\n        meta: { requiresAuth: true, title: '患者详情' }\r\n      },\r\n      {\r\n        path: '/reserveEdit',\r\n        name: 'ReserveEdit',\r\n        component: () => import('../views/admin/reserve/ReserveEdit'),\r\n        meta: { requiresAuth: true, title: '预约挂号修改' }\r\n      },\r\n      {\r\n        path: '/reserveManage',\r\n        name: 'ReserveManage',\r\n        component: () => import('../views/admin/reserve/ReserveManage'),\r\n        meta: { requiresAuth: true, title: '预约挂号管理' }\r\n      },\r\n      {\r\n        path: '/reserveDetail',\r\n        name: 'ReserveDetail',\r\n        component: () => import('../views/admin/reserve/ReserveDetail'),\r\n        meta: { requiresAuth: true, title: '预约挂号详情' }\r\n      },\r\n      {\r\n        path: '/mailremindertemplateEdit',\r\n        name: 'MailremindertemplateEdit',\r\n        component: () => import('../views/admin/mailremindertemplate/MailremindertemplateEdit'),\r\n        meta: { requiresAuth: true, title: '邮件模板设置' }\r\n      },\r\n      {\r\n        path: '/total1',\r\n        name: 'Total1',\r\n        component: () => import('../views/admin/total/Total1'),\r\n        meta: { requiresAuth: true, title: '科室预约统计' }\r\n      },\r\n      {\r\n        path: '/total2',\r\n        name: 'Total2',\r\n        component: () => import('../views/admin/total/Total2'),\r\n        meta: { requiresAuth: true, title: '医生预约统计' }\r\n      },\r\n      \r\n      \r\n\r\n      {\r\n        path: '/password',\r\n        name: 'Password',\r\n        component: () => import('../views/admin/system/Password'),\r\n        meta: {\r\n          requireAuth: true, title: '修改密码'\r\n        }\r\n      },\r\n    ]\r\n  },\r\n\r\n\r\n]\r\n\r\n\r\n\r\nconst router = createRouter({\r\n  history: createWebHistory(process.env.BASE_URL),\r\n  routes\r\n})\r\n\r\n\r\n\r\nrouter.beforeEach((to, from, next) => {\r\n  if (to.path == '/') {\r\n    sessionStorage.removeItem('userLname');\r\n    sessionStorage.removeItem('role');\r\n  }\r\n  let currentUser = sessionStorage.getItem('userLname');\r\n  console.log(to + \"  to.meta.requireAuth\");\r\n\r\n  if (to.meta.requireAuth) {\r\n    if (!currentUser && to.path != '/login') {\r\n      next({ path: '/' });\r\n    } else {\r\n      next();\r\n    }\r\n  } else {\r\n\r\n    next();\r\n  }\r\n})\r\n\r\nexport default router\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAE3D,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC;EACzCC,IAAI,EAAE;IACJC,WAAW,EAAE;EACf;AACF,CAAC,EAED;EACEJ,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,eAAe,CAAC;EACxCG,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC;IAC9CC,IAAI,EAAE;MACJC,WAAW,EAAE,IAAI;MAAEG,KAAK,EAAE;IAC5B;EAEF,CAAC,EAED;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC;IACjEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC;IAC1DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;IAC7DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;IAC7DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAS;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAO;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;IAC7DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAS;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC;IAC/DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAS;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC;IAC/DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAS;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,2BAA2B;IACjCC,IAAI,EAAE,0BAA0B;IAChCC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8DAA8D,CAAC;IACvFC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAS;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC;IACtDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAS;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC;IACtDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAAED,KAAK,EAAE;IAAS;EAC9C,CAAC,EAID;IACEP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDC,IAAI,EAAE;MACJC,WAAW,EAAE,IAAI;MAAEG,KAAK,EAAE;IAC5B;EACF,CAAC;AAEL,CAAC,CAGF;AAID,MAAME,MAAM,GAAGZ,YAAY,CAAC;EAC1Ba,OAAO,EAAEZ,gBAAgB,CAACa,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/Cd;AACF,CAAC,CAAC;AAIFU,MAAM,CAACK,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC,IAAIF,EAAE,CAACf,IAAI,IAAI,GAAG,EAAE;IAClBkB,cAAc,CAACC,UAAU,CAAC,WAAW,CAAC;IACtCD,cAAc,CAACC,UAAU,CAAC,MAAM,CAAC;EACnC;EACA,IAAIC,WAAW,GAAGF,cAAc,CAACG,OAAO,CAAC,WAAW,CAAC;EACrDC,OAAO,CAACC,GAAG,CAACR,EAAE,GAAG,uBAAuB,CAAC;EAEzC,IAAIA,EAAE,CAACZ,IAAI,CAACC,WAAW,EAAE;IACvB,IAAI,CAACgB,WAAW,IAAIL,EAAE,CAACf,IAAI,IAAI,QAAQ,EAAE;MACvCiB,IAAI,CAAC;QAAEjB,IAAI,EAAE;MAAI,CAAC,CAAC;IACrB,CAAC,MAAM;MACLiB,IAAI,CAAC,CAAC;IACR;EACF,CAAC,MAAM;IAELA,IAAI,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AAEF,eAAeR,MAAM", "ignoreList": []}]}