const App = getApp();
Page({
  data: {
    patients: [],
    loading: false,
  },

  onLoad() {
    this.getPatients();
  },

  onShow() {
    this.getPatients(); // 每次显示页面时刷新列表
  },

  // 获取就诊人列表
  getPatients() {
    this.setData({ loading: true });
    let param = {
      lname: wx.getStorageSync("lname"),
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/patient/list.action?currentPage=1&pageSize=100").then((data) => {
      this.setData({
        patients: data.resdata || [],
        loading: false,
      });
    });
  },

  // 删除就诊人
  deletePatient(e) {
    const id = e.currentTarget.dataset.id;
    wx.showModal({
      title: "提示",
      content: "确定要删除该就诊人吗？",
      success: (res) => {
        if (res.confirm) {
          let param = {
            peoid: id,
          };
          App.HttpService.saveData(param, "/patient/del.action?id=" + id).then((res) => {
            App.WxService.showToast({
              title: "删除成功!",
              icon: "success",
              duration: 1500,
            });
            setTimeout(() => {
              this.getPatients(); // 刷新列表
            }, 1500);
          });
        }
      },
    });
  },

  // 跳转到添加就诊人页面
  goToAdd() {
    wx.navigateTo({
      url: "/pages/patientAdd/index",
    });
  },
});
