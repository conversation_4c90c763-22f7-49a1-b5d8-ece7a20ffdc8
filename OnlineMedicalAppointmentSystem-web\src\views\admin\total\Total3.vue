﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
        <!-- 操作按钮 -->
        <div style="margin-bottom: 20px; text-align: right;">
            <el-button type="primary" @click="exportToExcel" :loading="exportLoading">
                <i class="el-icon-download"></i> 导出Excel
            </el-button>
        </div>

        <!-- 图表容器 -->
        <div class="echart" id="mychart" :style="myChartStyle"></div>
    </div>
</template>

<script>

 import * as echarts from "echarts";
 import request, { base } from "../../../../utils/http";
 import * as XLSX from 'xlsx';
 import { saveAs } from 'file-saver';

 export default {
   data() {
     return {
       myChartStyle: {
         height: "500px",
         width: "100%"
       },
       pieData: [],
       pieName: [],
       exportLoading: false,
       myChart: null,
       role: '',
       doctorId: null
     };
   },
   mounted() {
     // 获取用户角色和ID
     this.role = sessionStorage.getItem("role");
     const user = JSON.parse(sessionStorage.getItem("user") || '{}');
     if (this.role === '医生' && user.did) {
       this.doctorId = user.did;
     }

     this.getdata();
   },
   methods: {
 
     //数据初始化
     getdata() {
       let url = base + "/ReportData/queryReport3";

       let para = {};

       // 如果是医生，只查看自己的数据
       if (this.role === '医生' && this.doctorId) {
         para.by1 = this.doctorId.toString();
       }

       request.post(url, para).then((res) => {
         if (res.code == 200) {
           var ss = res.resdata;
           var pieName2 = [];
           var pieData2 = [];
           for (let i = 0; i < ss.length; i++) {
             pieName2[i] = ss[i].name;
             pieData2[i] = ss[i].num;
           }
           this.pieName = pieName2;
           this.pieData = pieData2;
           this.initEcharts();
         } else {
           this.$message.error(res.msg);
         }
       }).catch((error) => {
         console.error('获取数据失败:', error);
         this.$message.error('获取数据失败');
       });
     },
 
     initEcharts() {
       const chartDom = document.getElementById("mychart");
       this.myChart = echarts.init(chartDom);

       const title = this.role === '医生' ? '我的日预约统计' : '日预约统计';

       const option = {
         title: {
           text: title,
           left: 'center'
         },
         xAxis: {
           type: "category",
           data: this.pieName,
           name: '日期'
         },
         tooltip: {
           trigger: 'axis',
           axisPointer: {
             type: 'cross',
             label: {
               backgroundColor: '#6a7985'
             }
           },
           formatter: '{a} <br/>{b}: {c}'
         },
         yAxis: {
           type: "value",
           name: '预约数量'
         },
         series: [
           {
             name: '预约数量',
             data: this.pieData,
             type: "line",
             smooth: true,
             itemStyle: {
               color: '#409EFF'
             },
             areaStyle: {
               color: 'rgba(64, 158, 255, 0.2)'
             }
           }
         ]
       };
       this.myChart.setOption(option);

       //随着屏幕大小调节图表
       window.addEventListener("resize", () => {
         if (this.myChart) {
           this.myChart.resize();
         }
       });
     },

     // Excel导出功能
     exportToExcel() {
       this.exportLoading = true;

       try {
         // 准备导出数据
         const exportData = this.pieName.map((name, index) => ({
           '序号': index + 1,
           '日期': name,
           '预约数量': this.pieData[index] || 0
         }));

         // 创建工作簿
         const ws = XLSX.utils.json_to_sheet(exportData);
         const wb = XLSX.utils.book_new();
         const sheetName = this.role === '医生' ? '我的日预约统计' : '日预约统计';
         XLSX.utils.book_append_sheet(wb, ws, sheetName);

         // 设置列宽
         ws['!cols'] = [
           { wch: 8 },  // 序号
           { wch: 15 }, // 日期
           { wch: 12 }  // 预约数量
         ];

         // 导出文件
         const fileName = `${sheetName}_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`;
         XLSX.writeFile(wb, fileName);

         this.$message.success('导出成功！');
       } catch (error) {
         console.error('导出失败:', error);
         this.$message.error('导出失败，请重试');
       } finally {
         this.exportLoading = false;
       }
     }
   }
 };
</script>
<style scoped>
</style>
 

